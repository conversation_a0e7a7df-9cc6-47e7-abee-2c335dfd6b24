#ifndef UTILS_H
#define UTILS_H

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

namespace utils {

// 文件操作工具
std::vector<std::string> getImageFiles(const std::string& directory);
bool createDirectory(const std::string& path);

// 图像处理工具
cv::Mat enhanceImage(const cv::Mat& image);
void resizeImageKeepAspect(const cv::Mat& input, cv::Mat& output, cv::Size target_size);

// 标定辅助工具
void printCalibrationInfo(const cv::Mat& camera_matrix, const cv::Mat& dist_coeffs);
void printTransformationInfo(const cv::Mat& R, const cv::Mat& T);

// 数学工具
double calculateAngleBetweenVectors(const cv::Vec3d& v1, const cv::Vec3d& v2);
cv::<PERSON> createTransformationMatrix(const cv::Mat& R, const cv::Mat& T);

// 验证工具
bool validateImagePair(const cv::Mat& img1, const cv::Mat& img2);
double calculateImageSimilarity(const cv::Mat& img1, const cv::Mat& img2);

// 配置管理
struct CalibrationConfig {
    cv::Size board_size;
    float square_size;
    std::string normal_images_dir;
    std::string mirror_images_dir;
    std::string output_dir;
    bool save_intermediate_results;
    bool show_visualization;
    
    CalibrationConfig() :
        board_size(11, 8),
        square_size(10.0f),
        save_intermediate_results(true),
        show_visualization(true) {}
};

bool loadConfig(const std::string& config_file, CalibrationConfig& config);
bool saveConfig(const std::string& config_file, const CalibrationConfig& config);

} // namespace utils

#endif // UTILS_H
