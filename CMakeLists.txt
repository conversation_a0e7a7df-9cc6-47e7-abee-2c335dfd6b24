cmake_minimum_required(VERSION 3.15)
project(camera_calibrate)

set(CMAKE_CXX_STANDARD 20)

find_package(OpenCV REQUIRED)
set(PCL_DIR /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/share/pcl-1.14)
find_package(PCL REQUIRED)

add_executable(${PROJECT_NAME}
    main.cpp
    camera_calibrator.cpp
    utils.cpp
)

target_include_directories(${PROJECT_NAME} PRIVATE ${PCL_INCLUDE_DIRS})
target_link_libraries(${PROJECT_NAME} ${OpenCV_LIBS} ${PCL_LIBRARIES})
target_compile_definitions(${PROJECT_NAME} PRIVATE ${PCL_DEFINITIONS})
