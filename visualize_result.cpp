#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/calib3d.hpp>

int main() {
    // 加载标定结果
    cv::FileStorage fs("calibration_result.yml", cv::FileStorage::READ);
    if (!fs.isOpened()) {
        std::cerr << "无法打开标定结果文件" << std::endl;
        return -1;
    }
    
    cv::Mat R, t;
    double error;
    int board_width, board_height;
    float square_size;
    
    fs["R_mirror_to_normal"] >> R;
    fs["t_mirror_to_normal"] >> t;
    fs["reprojection_error"] >> error;
    fs["board_size_width"] >> board_width;
    fs["board_size_height"] >> board_height;
    fs["square_size"] >> square_size;
    fs.release();
    
    std::cout << "=== 标定结果分析 ===" << std::endl;
    std::cout << "旋转矩阵 R:" << std::endl << R << std::endl;
    std::cout << "平移向量 t:" << std::endl << t << std::endl;
    std::cout << "重投影误差: " << error << " 像素" << std::endl;
    
    // 计算旋转角度
    cv::Mat rvec;
    cv::Rodrigues(R, rvec);
    double angle = cv::norm(rvec) * 180.0 / CV_PI;
    std::cout << "旋转角度: " << angle << " 度" << std::endl;
    
    // 计算平移距离
    double distance = cv::norm(t);
    std::cout << "平移距离: " << distance << " mm" << std::endl;
    
    // 分析旋转轴
    cv::Mat axis_mat = rvec / cv::norm(rvec);
    std::cout << "旋转轴 (单位向量): [" << axis_mat.at<double>(0) << ", "
              << axis_mat.at<double>(1) << ", " << axis_mat.at<double>(2) << "]" << std::endl;
    
    // 示例：变换一个点
    cv::Mat point_mirror = (cv::Mat_<double>(3, 1) << 100, 200, 300);  // 镜像视角下的点
    cv::Mat point_normal = R * point_mirror + t;  // 变换到正常视角
    
    std::cout << "\n示例点变换:" << std::endl;
    std::cout << "镜像视角点: [" << point_mirror.at<double>(0) << ", " 
              << point_mirror.at<double>(1) << ", " << point_mirror.at<double>(2) << "]" << std::endl;
    std::cout << "正常视角点: [" << point_normal.at<double>(0) << ", " 
              << point_normal.at<double>(1) << ", " << point_normal.at<double>(2) << "]" << std::endl;
    
    std::cout << "\n=== 使用建议 ===" << std::endl;
    if (error < 1.0) {
        std::cout << "✓ 标定精度优秀，可以直接使用" << std::endl;
    } else if (error < 5.0) {
        std::cout << "✓ 标定精度可接受，建议在实际应用中验证" << std::endl;
    } else {
        std::cout << "⚠ 标定精度较差，建议：" << std::endl;
        std::cout << "  1. 检查相机内参是否正确" << std::endl;
        std::cout << "  2. 增加标定图像数量" << std::endl;
        std::cout << "  3. 改善图像质量和光照条件" << std::endl;
        std::cout << "  4. 确保棋盘格参数正确" << std::endl;
    }
    
    return 0;
}
