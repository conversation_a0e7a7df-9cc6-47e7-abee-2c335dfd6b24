# 相机镜像视角标定系统

## 概述

本系统实现了从"侧面镜像视角RGB"到"中间正常视角RGB"的相机标定算法，用于计算两个视角间的旋转矩阵R和平移矩阵T，最终实现将侧面视角获取的点云数据变换配准到中间正常视角的坐标系中。

## 功能特点

- **棋盘格角点检测**：自动检测正常视角和镜像视角图像中的棋盘格角点
- **镜像几何处理**：正确处理镜面反射的几何关系
- **PnP位姿估计**：使用PnP算法计算相机位姿
- **变换矩阵计算**：计算从镜像视角到正常视角的变换矩阵
- **精度验证**：通过重投影误差验证标定精度
- **可视化功能**：显示角点检测结果和标定结果
- **结果保存**：保存标定参数供后续使用

## 系统要求

- OpenCV 4.x
- PCL 1.14
- C++20
- CMake 3.15+

## 编译方法

```bash
mkdir build
cd build
cmake ..
make
```

## 使用方法

### 基本用法

```bash
# 使用默认路径
./camera_calibrate

# 指定图像目录
./camera_calibrate -n calibrate_imgs/leftmid/mid -m calibrate_imgs/leftmid/left

# 使用配置文件
./camera_calibrate -c config.yml

# 指定相机内参文件
./camera_calibrate --camera-params camera_params.yml
```

### 命令行参数

- `-h, --help`: 显示帮助信息
- `-n, --normal <dir>`: 正常视角图像目录
- `-m, --mirror <dir>`: 镜像视角图像目录
- `-o, --output <dir>`: 输出目录
- `--board-size <w>x<h>`: 棋盘格内角点数量（默认：9x6）
- `--square-size <size>`: 棋盘格方格大小，单位mm（默认：25.0）
- `--camera-params <file>`: 相机内参文件
- `--no-viz`: 不显示可视化结果

### 配置文件

复制 `config_example.yml` 并根据实际情况修改参数：

```yaml
board_width: 9
board_height: 6
square_size: 25.0
normal_images_dir: "calibrate_imgs/leftmid/mid"
mirror_images_dir: "calibrate_imgs/leftmid/left"
output_dir: "output"
save_intermediate_results: true
show_visualization: true
```

## 标定流程

1. **图像准备**：
   - 准备棋盘格标定板
   - 拍摄正常视角图像（直接拍摄棋盘格）
   - 拍摄镜像视角图像（通过镜子拍摄棋盘格）
   - 建议至少10对图像，覆盖不同位置和角度

2. **角点检测**：
   - 自动检测两个视角图像中的棋盘格角点
   - 处理镜像图像的几何变换
   - 可视化检测结果

3. **标定计算**：
   - 使用PnP算法计算每个视角的相机位姿
   - 计算从镜像视角到正常视角的变换矩阵
   - 验证标定精度

4. **结果输出**：
   - 保存变换矩阵R和T
   - 输出重投影误差
   - 生成标定报告

## 输出文件

- `calibration_results.yml`: 标定结果（R、T矩阵等）
- `calibration_config.yml`: 使用的配置参数
- `corners_pair_*.jpg`: 角点检测可视化结果（可选）

## 使用标定结果

标定完成后，可以使用变换矩阵将镜像视角的点云变换到正常视角：

```cpp
// 加载标定结果
CameraCalibrator calibrator;
calibrator.loadCalibrationResults("output/calibration_results.yml");

// 变换点云
std::vector<cv::Point3f> mirror_points;  // 镜像视角点云
std::vector<cv::Point3f> normal_points;  // 变换后的正常视角点云
calibrator.transformPointCloud(mirror_points, normal_points);
```

## 注意事项

1. **图像质量**：确保图像清晰，棋盘格完整可见
2. **镜面质量**：使用平整的镜面，避免变形
3. **标定板精度**：精确测量棋盘格方格大小
4. **图像数量**：建议使用10-20对图像以获得更好的精度
5. **覆盖范围**：图像应覆盖相机视野的不同区域

## 精度评估

- **优秀**：重投影误差 < 1.0 像素
- **良好**：重投影误差 < 2.0 像素
- **一般**：重投影误差 >= 2.0 像素（建议重新标定）

## 故障排除

1. **角点检测失败**：
   - 检查图像质量和棋盘格清晰度
   - 调整棋盘格参数设置
   - 确保光照均匀

2. **标定精度差**：
   - 增加标定图像数量
   - 改善图像覆盖范围
   - 检查相机内参是否正确

3. **镜像处理问题**：
   - 确保镜面平整无变形
   - 检查镜像图像的几何关系

## 技术支持

如有问题，请检查：
1. 依赖库版本是否正确
2. 图像路径是否存在
3. 棋盘格参数是否匹配实际标定板
4. 相机内参是否正确设置
