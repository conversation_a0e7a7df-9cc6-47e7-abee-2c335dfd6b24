#include <iostream>
#include <opencv2/opencv.hpp>
#include "camera_calibrator.h"
#include "utils.h"

void printUsage() {
    std::cout << "\n=== 相机镜像视角标定程序 ===" << std::endl;
    std::cout << "用法: ./camera_calibrate [选项]" << std::endl;
    std::cout << "\n选项:" << std::endl;
    std::cout << "  -h, --help              显示帮助信息" << std::endl;
    std::cout << "  -c, --config <file>     指定配置文件" << std::endl;
    std::cout << "  -n, --normal <dir>      正常视角图像目录" << std::endl;
    std::cout << "  -m, --mirror <dir>      镜像视角图像目录" << std::endl;
    std::cout << "  -o, --output <dir>      输出目录" << std::endl;
    std::cout << "  --board-size <w>x<h>    棋盘格内角点数量 (默认: 11x8)" << std::endl;
    std::cout << "  --square-size <size>    棋盘格方格大小(mm) (默认: 10.0)" << std::endl;
    std::cout << "  --camera-params <file>  相机内参文件" << std::endl;
    std::cout << "  --no-viz               不显示可视化结果" << std::endl;
    std::cout << "\n示例:" << std::endl;
    std::cout << "  ./camera_calibrate -n calibrate_imgs/leftmid/mid -m calibrate_imgs/leftmid/left" << std::endl;
    std::cout << "=========================" << std::endl;
}

bool parseArguments(int argc, char* argv[], utils::CalibrationConfig& config,
                   std::string& camera_params_file) {
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            printUsage();
            return false;
        }
        else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                utils::loadConfig(argv[++i], config);
            }
        }
        else if (arg == "-n" || arg == "--normal") {
            if (i + 1 < argc) {
                config.normal_images_dir = argv[++i];
            }
        }
        else if (arg == "-m" || arg == "--mirror") {
            if (i + 1 < argc) {
                config.mirror_images_dir = argv[++i];
            }
        }
        else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                config.output_dir = argv[++i];
            }
        }
        else if (arg == "--board-size") {
            if (i + 1 < argc) {
                std::string size_str = argv[++i];
                size_t x_pos = size_str.find('x');
                if (x_pos != std::string::npos) {
                    int width = std::stoi(size_str.substr(0, x_pos));
                    int height = std::stoi(size_str.substr(x_pos + 1));
                    config.board_size = cv::Size(width, height);
                }
            }
        }
        else if (arg == "--square-size") {
            if (i + 1 < argc) {
                config.square_size = std::stof(argv[++i]);
            }
        }
        else if (arg == "--camera-params") {
            if (i + 1 < argc) {
                camera_params_file = argv[++i];
            }
        }
        else if (arg == "--no-viz") {
            config.show_visualization = false;
        }
    }

    return true;
}

int main(int argc, char* argv[]) {
    std::cout << "\n=== 相机镜像视角标定程序 ===" << std::endl;
    std::cout << "版本: 1.0" << std::endl;
    std::cout << "功能: 计算从侧面镜像视角到中间正常视角的变换矩阵" << std::endl;
    std::cout << "=========================" << std::endl;

    // 解析命令行参数
    utils::CalibrationConfig config;
    std::string camera_params_file;

    if (!parseArguments(argc, argv, config, camera_params_file)) {
        return 0;
    }

    // 设置默认路径
    if (config.normal_images_dir.empty()) {
        config.normal_images_dir = "calibrate_imgs/leftmid/mid";
    }
    if (config.mirror_images_dir.empty()) {
        config.mirror_images_dir = "calibrate_imgs/leftmid/left";
    }
    if (config.output_dir.empty()) {
        config.output_dir = "output";
    }

    // 创建输出目录
    utils::createDirectory(config.output_dir);

    std::cout << "\n配置信息:" << std::endl;
    std::cout << "正常视角图像目录: " << config.normal_images_dir << std::endl;
    std::cout << "镜像视角图像目录: " << config.mirror_images_dir << std::endl;
    std::cout << "输出目录: " << config.output_dir << std::endl;
    std::cout << "棋盘格大小: " << config.board_size.width << "x" << config.board_size.height << std::endl;
    std::cout << "方格大小: " << config.square_size << "mm" << std::endl;

    // 创建标定器
    CameraCalibrator calibrator;
    calibrator.setBoardSize(config.board_size.width, config.board_size.height);
    calibrator.setSquareSize(config.square_size);

    // 加载相机内参（如果提供）
    if (!camera_params_file.empty()) {
        try {
            cv::FileStorage fs(camera_params_file, cv::FileStorage::READ);
            if (fs.isOpened()) {
                cv::Mat camera_matrix, dist_coeffs;
                fs["camera_matrix"] >> camera_matrix;
                fs["dist_coeffs"] >> dist_coeffs;

                if (!camera_matrix.empty() && !dist_coeffs.empty()) {
                    calibrator.setCameraMatrix(camera_matrix, dist_coeffs);
                    std::cout << "\n已加载相机内参文件: " << camera_params_file << std::endl;
                    utils::printCalibrationInfo(camera_matrix, dist_coeffs);
                } else {
                    std::cerr << "警告: 相机内参文件格式不正确，使用默认参数" << std::endl;
                    goto use_default_params;
                }
                fs.release();
            } else {
                std::cerr << "警告: 无法打开相机内参文件: " << camera_params_file << "，使用默认参数" << std::endl;
                goto use_default_params;
            }
        } catch (const cv::Exception& e) {
            std::cerr << "警告: 读取相机内参文件时出错: " << e.what() << "，使用默认参数" << std::endl;
            goto use_default_params;
        }
    } else {
        use_default_params:
        // 使用默认的相机内参（需要根据实际相机调整）
        cv::Mat camera_matrix = (cv::Mat_<double>(3, 3) <<
            800, 0, 320,
            0, 800, 240,
            0, 0, 1);
        cv::Mat dist_coeffs = cv::Mat::zeros(5, 1, CV_64F);

        calibrator.setCameraMatrix(camera_matrix, dist_coeffs);
        std::cout << "\n使用默认相机内参（请根据实际相机调整）" << std::endl;
        utils::printCalibrationInfo(camera_matrix, dist_coeffs);
    }

    // 步骤1: 加载图像对
    std::cout << "\n步骤1: 加载图像对..." << std::endl;
    if (!calibrator.loadImagePairs(config.normal_images_dir, config.mirror_images_dir)) {
        std::cerr << "错误: 无法加载图像对" << std::endl;
        return -1;
    }

    // 步骤2: 检测棋盘格角点
    std::cout << "\n步骤2: 检测棋盘格角点..." << std::endl;
    if (!calibrator.detectChessboardCorners()) {
        std::cerr << "错误: 角点检测失败" << std::endl;
        return -1;
    }

    // 可视化检测结果
    if (config.show_visualization) {
        std::cout << "\n显示角点检测结果（按任意键继续）..." << std::endl;
        calibrator.visualizeDetectedCorners(config.save_intermediate_results);
    }

    // 步骤3: 执行标定
    std::cout << "\n步骤3: 执行标定计算..." << std::endl;
    if (!calibrator.calibrate()) {
        std::cerr << "错误: 标定失败" << std::endl;
        return -1;
    }

    // 步骤4: 显示和保存结果
    std::cout << "\n步骤4: 保存标定结果..." << std::endl;
    const auto& calib_data = calibrator.getCalibrationData();

    // 显示结果
    calibrator.visualizeCalibrationResults();

    // 保存标定结果
    std::string result_file = config.output_dir + "/calibration_results.yml";
    if (calibrator.saveCalibrationResults(result_file)) {
        std::cout << "标定结果已保存到: " << result_file << std::endl;
    }

    // 保存配置文件
    std::string config_file = config.output_dir + "/calibration_config.yml";
    if (utils::saveConfig(config_file, config)) {
        std::cout << "配置文件已保存到: " << config_file << std::endl;
    }

    // 验证标定精度
    std::cout << "\n步骤5: 验证标定精度..." << std::endl;
    double validation_error = calibrator.validateCalibration();
    if (validation_error >= 0) {
        std::cout << "验证重投影误差: " << validation_error << " 像素" << std::endl;

        if (validation_error < 1.0) {
            std::cout << "✓ 标定精度优秀 (< 1.0 像素)" << std::endl;
        } else if (validation_error < 2.0) {
            std::cout << "✓ 标定精度良好 (< 2.0 像素)" << std::endl;
        } else {
            std::cout << "⚠ 标定精度一般 (>= 2.0 像素)，建议重新标定" << std::endl;
        }
    }

    std::cout << "\n=== 标定完成 ===" << std::endl;
    std::cout << "变换矩阵可用于将侧面镜像视角的点云数据变换到中间正常视角坐标系" << std::endl;
    std::cout << "使用 transformPointCloud() 函数应用变换" << std::endl;

    return 0;
}