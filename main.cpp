#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/calib3d.hpp>
#include <filesystem>
#include <vector>
#include <algorithm>

// 全局配置
cv::Size board_size(11, 8);  // 棋盘格内角点数量
float square_size = 10.0f;   // 方格大小(mm)
std::string normal_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/mid";
std::string mirror_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/left";


// 根据图像尺寸调整相机内参
cv::Mat adjustCameraMatrix(const cv::Mat& original_matrix, cv::Size original_size, cv::Size new_size) {
    double scale_x = static_cast<double>(new_size.width) / original_size.width;
    double scale_y = static_cast<double>(new_size.height) / original_size.height;

    cv::Mat adjusted = original_matrix.clone();
    adjusted.at<double>(0, 0) *= scale_x;  // fx
    adjusted.at<double>(1, 1) *= scale_y;  // fy
    adjusted.at<double>(0, 2) *= scale_x;  // cx
    adjusted.at<double>(1, 2) *= scale_y;  // cy

    return adjusted;
}

// 加载图像文件
std::vector<std::string> loadImageFiles(const std::string& dir) {
    std::vector<std::string> files;
    for (const auto& entry : std::filesystem::directory_iterator(dir)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".jpg" || ext == ".png" || ext == ".bmp") {
                files.push_back(entry.path().string());
            }
        }
    }
    std::sort(files.begin(), files.end());
    return files;
}

// 检测棋盘格角点（带图像缩放优化）
bool detectChessboard(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);

    // 计算缩放比例
    double scale = 0.5;
    cv::Mat scaled_gray = gray;

    cv::resize(gray, scaled_gray, cv::Size(), scale, scale);

//    std::cout << "    图像缩放比例: " << scale << " (" << gray.cols << "x" << gray.rows
//              << " -> " << scaled_gray.cols << "x" << scaled_gray.rows << ")" << std::endl;


    std::vector<cv::Point2f> scaled_corners;
    bool found = cv::findChessboardCorners(scaled_gray, board_size, scaled_corners,
                                          cv::CALIB_CB_ADAPTIVE_THRESH |
                                          cv::CALIB_CB_NORMALIZE_IMAGE);

    if (found) {
        // 在缩放图像上进行亚像素优化
        cv::cornerSubPix(scaled_gray, scaled_corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));

        // 将角点坐标放缩回原图尺寸
        corners.clear();
        for (const auto& corner : scaled_corners) {
            corners.push_back(cv::Point2f(corner.x / scale, corner.y / scale));
        }

        // 在原图上进行最终的亚像素优化
        cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));
    }

    return found;
}

// 生成3D棋盘格角点
std::vector<cv::Point3f> generateObjectPoints() {
    std::vector<cv::Point3f> corners;
    for (int i = 0; i < board_size.height; ++i) {
        for (int j = 0; j < board_size.width; ++j) {
            corners.push_back(cv::Point3f(j * square_size, i * square_size, 0));
        }
    }
    return corners;
}



int main() {
    std::cout << "\n=== 相机镜像视角标定程序 ===" << std::endl;
    std::cout << "棋盘格: " << board_size.width << "x" << board_size.height
              << ", 方格大小: " << square_size << "mm" << std::endl;

    // 步骤1: 加载图像
    std::cout << "\n步骤1: 加载图像..." << std::endl;
    auto normal_files = loadImageFiles(normal_dir);
    auto mirror_files = loadImageFiles(mirror_dir);

    if (normal_files.empty() || mirror_files.empty()) {
        std::cerr << "错误: 无法找到图像文件" << std::endl;
        return -1;
    }

    size_t num_pairs = std::min(normal_files.size(), mirror_files.size());
    std::cout << "找到 " << num_pairs << " 对图像" << std::endl;

    // 步骤2: 检测角点并收集数据
    std::cout << "\n步骤2: 检测棋盘格角点..." << std::endl;
    std::vector<std::vector<cv::Point2f>> normal_image_points, mirror_image_points;
    std::vector<std::vector<cv::Point3f>> object_points;
    auto object_corners = generateObjectPoints();

    for (size_t i = 0; i < num_pairs; ++i) {

        cv::Mat normal_img = cv::imread(normal_files[i]);
        cv::Mat mirror_img = cv::imread(mirror_files[i]);

        if (normal_img.empty() || mirror_img.empty()) {
            std::cout << "  跳过：图像加载失败" << std::endl;
            continue;
        }


        std::vector<cv::Point2f> normal_corners, mirror_corners;

        bool normal_found = detectChessboard(normal_img, normal_corners);
        bool mirror_found = detectChessboard(mirror_img, mirror_corners);


        if (normal_found && mirror_found) {
            // 不对镜像图像进行翻转，保持原始几何关系
            normal_image_points.push_back(normal_corners);
            mirror_image_points.push_back(mirror_corners);
            object_points.push_back(object_corners);

            std::cout << "  ✓ 图像对 " << (i+1) << " 检测成功" << std::endl;
        } else {
            std::cout << "  ✗ 图像对 " << (i+1) << " 检测失败" << std::endl;
        }
    }

    if (normal_image_points.size() < 3) {
        std::cerr << "错误: 需要至少3对有效图像进行标定" << std::endl;
        return -1;
    }

    std::cout << "成功检测 " << normal_image_points.size() << " 对图像的角点" << std::endl;



    // 获取图像尺寸
    cv::Size image_size(6000, 8000);  // 原始图像尺寸

    // 步骤3.1: 分别标定两个视角的相机内参
    std::cout << "  步骤3.1: 标定正常视角相机内参..." << std::endl;
    cv::Mat camera_matrix1, dist_coeffs1;
    std::vector<cv::Mat> rvecs1, tvecs1;

    double rms1 = cv::calibrateCamera(
        object_points,
        normal_image_points,
        image_size,
        camera_matrix1,
        dist_coeffs1,
        rvecs1,
        tvecs1,
        cv::CALIB_RATIONAL_MODEL
    );

    std::cout << "    正常视角标定完成，RMS误差: " << rms1 << std::endl;

    std::cout << "  步骤3.2: 标定镜像视角相机内参..." << std::endl;
    cv::Mat camera_matrix2, dist_coeffs2;
    std::vector<cv::Mat> rvecs2, tvecs2;

    double rms2 = cv::calibrateCamera(
        object_points,
        mirror_image_points,
        image_size,
        camera_matrix2,
        dist_coeffs2,
        rvecs2,
        tvecs2,
        cv::CALIB_RATIONAL_MODEL
    );

    std::cout << "    镜像视角标定完成，RMS误差: " << rms2 << std::endl;

    cv::Mat R, T, E, F;  // 输出参数

    std::cout << "  步骤3.3: 使用已标定的内参进行立体标定..." << std::endl;
    // 执行立体标定（使用已标定的内参，只计算外参）
    double rms_error = cv::stereoCalibrate(
        object_points,           // 3D点
        normal_image_points,     // 正常视角图像点
        mirror_image_points,     // 镜像视角图像点
        camera_matrix1,          // 正常视角相机内参（固定）
        dist_coeffs1,           // 正常视角畸变系数（固定）
        camera_matrix2,          // 镜像视角相机内参（固定）
        dist_coeffs2,           // 镜像视角畸变系数（固定）
        image_size,             // 图像尺寸
        R,                      // 输出：旋转矩阵
        T,                      // 输出：平移向量
        E,                      // 输出：本质矩阵
        F,                      // 输出：基础矩阵
        cv::CALIB_FIX_INTRINSIC,  // 固定内参，只优化外参
        cv::TermCriteria(cv::TermCriteria::COUNT + cv::TermCriteria::EPS, 100, 1e-6)
    );

    std::cout << "  立体标定完成，RMS误差: " << rms_error << std::endl;

    // 步骤4: 验证和显示结果
    std::cout << "\n步骤4: 验证标定结果..." << std::endl;

    // 显示相机内参标定结果
    std::cout << "\n=== 相机内参标定结果 ===" << std::endl;
    std::cout << "正常视角相机内参矩阵 (RMS=" << rms1 << "):" << std::endl << camera_matrix1 << std::endl;
    std::cout << "正常视角畸变系数:" << std::endl << dist_coeffs1.t() << std::endl;
    std::cout << "镜像视角相机内参矩阵 (RMS=" << rms2 << "):" << std::endl << camera_matrix2 << std::endl;
    std::cout << "镜像视角畸变系数:" << std::endl << dist_coeffs2.t() << std::endl;

    // 显示立体标定结果
    std::cout << "\n=== 立体标定结果 ===" << std::endl;
    std::cout << "旋转矩阵 R (镜像->正常):" << std::endl << R << std::endl;
    std::cout << "平移向量 T (镜像->正常):" << std::endl << T << std::endl;
    std::cout << "RMS重投影误差: " << rms_error << " 像素" << std::endl;

    // 计算旋转角度和平移距离
    cv::Mat rvec;
    cv::Rodrigues(R, rvec);
    double angle = cv::norm(rvec) * 180.0 / CV_PI;
    double distance = cv::norm(T);

    std::cout << "旋转角度: " << angle << " 度" << std::endl;
    std::cout << "平移距离: " << distance << " mm" << std::endl;

    // 步骤5: 可视化投影偏差
    std::cout << "\n步骤5: 可视化投影偏差..." << std::endl;

    for (size_t i = 0; i < std::min(normal_image_points.size(), size_t(3)); ++i) {
        // 加载原始图像用于可视化
        cv::Mat normal_img = cv::imread(normal_files[i]);
        cv::Mat mirror_img = cv::imread(mirror_files[i]);

        if (normal_img.empty() || mirror_img.empty()) continue;

        // 缩放图像用于显示
        cv::Mat normal_display, mirror_display;
        double display_scale = 0.2;  // 缩放到20%用于显示
        cv::resize(normal_img, normal_display, cv::Size(), display_scale, display_scale);
        cv::resize(mirror_img, mirror_display, cv::Size(), display_scale, display_scale);

        // 缩放角点坐标
        std::vector<cv::Point2f> normal_corners_display, mirror_corners_display;
        for (const auto& pt : normal_image_points[i]) {
            normal_corners_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }
        for (const auto& pt : mirror_image_points[i]) {
            mirror_corners_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }

        // 将镜像视角的3D点投影到正常视角
        std::vector<cv::Point3f> transformed_3d_points;
        for (const auto& pt3d : object_points[i]) {
            cv::Mat pt_mat = (cv::Mat_<double>(3, 1) << pt3d.x, pt3d.y, pt3d.z);
            cv::Mat transformed_pt = R * pt_mat + T;
            transformed_3d_points.push_back(cv::Point3f(
                transformed_pt.at<double>(0),
                transformed_pt.at<double>(1),
                transformed_pt.at<double>(2)
            ));
        }

        // 将变换后的3D点投影到正常视角图像
        std::vector<cv::Point2f> projected_points;
        cv::Mat rvec_identity = cv::Mat::zeros(3, 1, CV_64F);
        cv::Mat tvec_identity = cv::Mat::zeros(3, 1, CV_64F);
        cv::projectPoints(transformed_3d_points, rvec_identity, tvec_identity,
                         camera_matrix1, dist_coeffs1, projected_points);

        // 缩放投影点
        std::vector<cv::Point2f> projected_points_display;
        for (const auto& pt : projected_points) {
            projected_points_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }

        // 在正常视角图像上绘制
        cv::Mat result_img = normal_display.clone();

        // 绘制正常视角检测到的角点（绿色圆圈）
        for (const auto& pt : normal_corners_display) {
            cv::circle(result_img, pt, 8, cv::Scalar(0, 255, 0), 2);
        }

        // 绘制从镜像视角投影过来的点（红色十字）
        for (const auto& pt : projected_points_display) {
            cv::drawMarker(result_img, pt, cv::Scalar(0, 0, 255), cv::MARKER_CROSS, 16, 2);
        }

        // 绘制连接线显示偏差（蓝色线）
        for (size_t j = 0; j < std::min(normal_corners_display.size(), projected_points_display.size()); ++j) {
            cv::line(result_img, normal_corners_display[j], projected_points_display[j],
                    cv::Scalar(255, 0, 0), 1);
        }

        // 计算平均偏差
        double total_error = 0.0;
        for (size_t j = 0; j < std::min(normal_corners_display.size(), projected_points_display.size()); ++j) {
            cv::Point2f diff = normal_corners_display[j] - projected_points_display[j];
            total_error += sqrt(diff.x * diff.x + diff.y * diff.y);
        }
        double avg_error = total_error / std::min(normal_corners_display.size(), projected_points_display.size());

        // 添加图例和信息
        cv::putText(result_img, "Green: Normal view corners", cv::Point(10, 30),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
        cv::putText(result_img, "Red: Projected from mirror", cv::Point(10, 60),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 255), 2);
        cv::putText(result_img, "Blue: Error lines", cv::Point(10, 90),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 0, 0), 2);
        cv::putText(result_img, "Avg error: " + std::to_string(avg_error / display_scale) + " pixels",
                   cv::Point(10, 120), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);

        // 显示结果
        std::string window_name = "Projection Error Visualization - Image " + std::to_string(i + 1);
        cv::namedWindow(window_name, cv::WINDOW_NORMAL);
        cv::imshow(window_name, result_img);

        std::cout << "  图像 " << (i + 1) << " 平均投影误差: " << (avg_error / display_scale) << " 像素" << std::endl;
    }

    std::cout << "按任意键继续..." << std::endl;
    cv::waitKey(0);
    cv::destroyAllWindows();

    // 精度评估
    if (rms_error < 1.0) {
        std::cout << "✓ 标定精度优秀" << std::endl;
    } else if (rms_error < 2.0) {
        std::cout << "✓ 标定精度良好" << std::endl;
    } else {
        std::cout << "⚠ 标定精度一般，建议重新标定" << std::endl;
    }


    return 0;
}