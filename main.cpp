#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/calib3d.hpp>
#include <filesystem>
#include <vector>
#include <algorithm>

// 全局配置
cv::Size board_size(11, 8);  // 棋盘格内角点数量
float square_size = 10.0f;   // 方格大小(mm)
std::string normal_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/mid";
std::string mirror_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/left";
cv::Mat camera_matrix = (cv::Mat_<double>(3, 3) << 800, 0, 320, 0, 800, 240, 0, 0, 1);
cv::Mat dist_coeffs = cv::Mat::zeros(5, 1, CV_64F);

// 根据图像尺寸调整相机内参
cv::Mat adjustCameraMatrix(const cv::Mat& original_matrix, cv::Size original_size, cv::Size new_size) {
    double scale_x = static_cast<double>(new_size.width) / original_size.width;
    double scale_y = static_cast<double>(new_size.height) / original_size.height;

    cv::Mat adjusted = original_matrix.clone();
    adjusted.at<double>(0, 0) *= scale_x;  // fx
    adjusted.at<double>(1, 1) *= scale_y;  // fy
    adjusted.at<double>(0, 2) *= scale_x;  // cx
    adjusted.at<double>(1, 2) *= scale_y;  // cy

    return adjusted;
}

// 加载图像文件
std::vector<std::string> loadImageFiles(const std::string& dir) {
    std::vector<std::string> files;
    for (const auto& entry : std::filesystem::directory_iterator(dir)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".jpg" || ext == ".png" || ext == ".bmp") {
                files.push_back(entry.path().string());
            }
        }
    }
    std::sort(files.begin(), files.end());
    return files;
}

// 检测棋盘格角点（带图像缩放优化）
bool detectChessboard(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);

    // 计算缩放比例
    double scale = 0.5;
    cv::Mat scaled_gray = gray;

    cv::resize(gray, scaled_gray, cv::Size(), scale, scale);
    std::cout << "    图像缩放比例: " << scale << " (" << gray.cols << "x" << gray.rows
              << " -> " << scaled_gray.cols << "x" << scaled_gray.rows << ")" << std::endl;


    std::vector<cv::Point2f> scaled_corners;
    bool found = cv::findChessboardCorners(scaled_gray, board_size, scaled_corners,
                                          cv::CALIB_CB_ADAPTIVE_THRESH |
                                          cv::CALIB_CB_NORMALIZE_IMAGE);

    if (found) {
        // 在缩放图像上进行亚像素优化
        cv::cornerSubPix(scaled_gray, scaled_corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));

        // 将角点坐标放缩回原图尺寸
        corners.clear();
        for (const auto& corner : scaled_corners) {
            corners.push_back(cv::Point2f(corner.x / scale, corner.y / scale));
        }

        // 在原图上进行最终的亚像素优化
        cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));
    }

    return found;
}

// 生成3D棋盘格角点
std::vector<cv::Point3f> generateObjectPoints() {
    std::vector<cv::Point3f> corners;
    for (int i = 0; i < board_size.height; ++i) {
        for (int j = 0; j < board_size.width; ++j) {
            corners.push_back(cv::Point3f(j * square_size, i * square_size, 0));
        }
    }
    return corners;
}

// 计算重投影误差
double calculateReprojectionError(const std::vector<cv::Point2f>& image_points,
                                 const std::vector<cv::Point3f>& object_points,
                                 const cv::Mat& rvec, const cv::Mat& tvec) {
    std::vector<cv::Point2f> projected_points;
    cv::projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs, projected_points);

    double error = 0.0;
    for (size_t i = 0; i < image_points.size(); ++i) {
        cv::Point2f diff = image_points[i] - projected_points[i];
        error += sqrt(diff.x * diff.x + diff.y * diff.y);
    }
    return error / image_points.size();
}

int main() {
    std::cout << "\n=== 相机镜像视角标定程序 ===" << std::endl;
    std::cout << "棋盘格: " << board_size.width << "x" << board_size.height
              << ", 方格大小: " << square_size << "mm" << std::endl;

    // 步骤1: 加载图像
    std::cout << "\n步骤1: 加载图像..." << std::endl;
    auto normal_files = loadImageFiles(normal_dir);
    auto mirror_files = loadImageFiles(mirror_dir);

    if (normal_files.empty() || mirror_files.empty()) {
        std::cerr << "错误: 无法找到图像文件" << std::endl;
        return -1;
    }

    size_t num_pairs = std::min(normal_files.size(), mirror_files.size());
    std::cout << "找到 " << num_pairs << " 对图像" << std::endl;

    // 步骤2: 检测角点并收集数据
    std::cout << "\n步骤2: 检测棋盘格角点..." << std::endl;
    std::vector<std::vector<cv::Point2f>> normal_image_points, mirror_image_points;
    std::vector<std::vector<cv::Point3f>> object_points;
    auto object_corners = generateObjectPoints();

    for (size_t i = 0; i < num_pairs; ++i) {
        std::cout << "处理图像对 " << (i+1) << "/" << num_pairs << "..." << std::endl;

        cv::Mat normal_img = cv::imread(normal_files[i]);
        cv::Mat mirror_img = cv::imread(mirror_files[i]);

        if (normal_img.empty() || mirror_img.empty()) {
            std::cout << "  跳过：图像加载失败" << std::endl;
            continue;
        }

        std::cout << "  正常视角图像: " << normal_img.cols << "x" << normal_img.rows << std::endl;
        std::cout << "  镜像视角图像: " << mirror_img.cols << "x" << mirror_img.rows << std::endl;

        std::vector<cv::Point2f> normal_corners, mirror_corners;

        std::cout << "  检测正常视角角点..." << std::endl;
        bool normal_found = detectChessboard(normal_img, normal_corners);
        std::cout << "  正常视角检测: " << (normal_found ? "成功" : "失败") << std::endl;

        std::cout << "  检测镜像视角角点..." << std::endl;
        bool mirror_found = detectChessboard(mirror_img, mirror_corners);
        std::cout << "  镜像视角检测: " << (mirror_found ? "成功" : "失败") << std::endl;

        if (normal_found && mirror_found) {
            // 处理镜像图像（水平翻转）
            cv::flip(mirror_img, mirror_img, 1);
            for (auto& corner : mirror_corners) {
                corner.x = mirror_img.cols - corner.x;
            }

            normal_image_points.push_back(normal_corners);
            mirror_image_points.push_back(mirror_corners);
            object_points.push_back(object_corners);

            //绘制显示测试
            cv::drawChessboardCorners(normal_img, board_size, normal_corners, normal_found);
            cv::drawChessboardCorners(mirror_img, board_size, mirror_corners, mirror_found);
            cv::namedWindow("Normal", cv::WINDOW_NORMAL);
            cv::namedWindow("Mirror", cv::WINDOW_NORMAL);
            cv::imshow("Normal", normal_img);
            cv::imshow("Mirror", mirror_img);
            cv::waitKey(0);

            std::cout << "  ✓ 图像对 " << (i+1) << " 检测成功" << std::endl;
        } else {
            std::cout << "  ✗ 图像对 " << (i+1) << " 检测失败" << std::endl;
        }
    }

    if (normal_image_points.size() < 3) {
        std::cerr << "错误: 需要至少3对有效图像进行标定" << std::endl;
        return -1;
    }

    std::cout << "成功检测 " << normal_image_points.size() << " 对图像的角点" << std::endl;

    // 步骤3: 计算相机位姿
    std::cout << "\n步骤3: 计算相机位姿..." << std::endl;
    std::vector<cv::Mat> normal_rvecs, normal_tvecs, mirror_rvecs, mirror_tvecs;

    for (size_t i = 0; i < normal_image_points.size(); ++i) {
        cv::Mat rvec, tvec;

        // 正常视角位姿
        if (cv::solvePnP(object_points[i], normal_image_points[i], camera_matrix, dist_coeffs, rvec, tvec)) {
            normal_rvecs.push_back(rvec.clone());
            normal_tvecs.push_back(tvec.clone());
        }

        // 镜像视角位姿
        if (cv::solvePnP(object_points[i], mirror_image_points[i], camera_matrix, dist_coeffs, rvec, tvec)) {
            mirror_rvecs.push_back(rvec.clone());
            mirror_tvecs.push_back(tvec.clone());
        }
    }

    // 步骤4: 计算变换矩阵
    std::cout << "\n步骤4: 计算变换矩阵..." << std::endl;
    cv::Mat R_total = cv::Mat::zeros(3, 3, CV_64F);
    cv::Mat t_total = cv::Mat::zeros(3, 1, CV_64F);

    for (size_t i = 0; i < normal_rvecs.size(); ++i) {
        cv::Mat R_normal, R_mirror;
        cv::Rodrigues(normal_rvecs[i], R_normal);
        cv::Rodrigues(mirror_rvecs[i], R_mirror);

        // 计算相对变换: R_rel = R_normal * R_mirror^T
        cv::Mat R_rel = R_normal * R_mirror.t();
        cv::Mat t_rel = normal_tvecs[i] - R_rel * mirror_tvecs[i];

        R_total += R_rel;
        t_total += t_rel;
    }

    R_total /= static_cast<double>(normal_rvecs.size());
    t_total /= static_cast<double>(normal_rvecs.size());

    // 步骤5: 验证和显示结果
    std::cout << "\n步骤5: 验证标定结果..." << std::endl;

    // 计算重投影误差
    double total_error = 0.0;
    for (size_t i = 0; i < normal_image_points.size(); ++i) {
        double error_normal = calculateReprojectionError(normal_image_points[i], object_points[i],
                                                        normal_rvecs[i], normal_tvecs[i]);

        // 变换镜像位姿并计算误差
        cv::Mat R_mirror;
        cv::Rodrigues(mirror_rvecs[i], R_mirror);
        cv::Mat R_transformed = R_total * R_mirror;
        cv::Mat t_transformed = R_total * mirror_tvecs[i] + t_total;
        cv::Mat rvec_transformed;
        cv::Rodrigues(R_transformed, rvec_transformed);

        double error_mirror = calculateReprojectionError(normal_image_points[i], object_points[i],
                                                        rvec_transformed, t_transformed);

        total_error += (error_normal + error_mirror) / 2.0;
    }

    double avg_error = total_error / normal_image_points.size();

    // 显示结果
    std::cout << "\n=== 标定结果 ===" << std::endl;
    std::cout << "变换矩阵 R (镜像->正常):" << std::endl << R_total << std::endl;
    std::cout << "平移向量 t (镜像->正常):" << std::endl << t_total << std::endl;
    std::cout << "平均重投影误差: " << avg_error << " 像素" << std::endl;

    if (avg_error < 1.0) {
        std::cout << "✓ 标定精度优秀" << std::endl;
    } else if (avg_error < 2.0) {
        std::cout << "✓ 标定精度良好" << std::endl;
    } else {
        std::cout << "⚠ 标定精度一般，建议重新标定" << std::endl;
    }

    // 保存结果
    cv::FileStorage fs("calibration_result.yml", cv::FileStorage::WRITE);
    fs << "R_mirror_to_normal" << R_total;
    fs << "t_mirror_to_normal" << t_total;
    fs << "reprojection_error" << avg_error;
    fs << "board_size_width" << board_size.width;
    fs << "board_size_height" << board_size.height;
    fs << "square_size" << square_size;
    fs.release();

    std::cout << "结果已保存到: calibration_result.yml" << std::endl;
    std::cout << "=== 标定完成 ===" << std::endl;

    return 0;
}