#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/calib3d.hpp>
#include <filesystem>
#include <vector>
#include <algorithm>

// 全局配置
cv::Size board_size(11, 8);  // 棋盘格内角点数量
float square_size = 10.0f;   // 方格大小(mm)
std::string normal_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/mid";
std::string mirror_dir = "/Users/<USER>/work/Proj/camera_calibrate/calibrate_imgs/leftmid/left";



// 加载图像文件
std::vector<std::string> loadImageFiles(const std::string& dir) {
    std::vector<std::string> files;
    for (const auto& entry : std::filesystem::directory_iterator(dir)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".jpg" || ext == ".png" || ext == ".bmp") {
                files.push_back(entry.path().string());
            }
        }
    }
    std::sort(files.begin(), files.end());
    return files;
}

// 检测棋盘格角点（带图像缩放优化）
bool detectChessboard(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);

    // 计算缩放比例
    double scale = 0.5;
    cv::Mat scaled_gray = gray;

    cv::resize(gray, scaled_gray, cv::Size(), scale, scale);

//    std::cout << "    图像缩放比例: " << scale << " (" << gray.cols << "x" << gray.rows
//              << " -> " << scaled_gray.cols << "x" << scaled_gray.rows << ")" << std::endl;


    std::vector<cv::Point2f> scaled_corners;
    bool found = cv::findChessboardCorners(scaled_gray, board_size, scaled_corners,
                                          cv::CALIB_CB_ADAPTIVE_THRESH |
                                          cv::CALIB_CB_NORMALIZE_IMAGE);

    if (found) {
        // 在缩放图像上进行亚像素优化
        cv::cornerSubPix(scaled_gray, scaled_corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));

        // 将角点坐标放缩回原图尺寸
        corners.clear();
        for (const auto& corner : scaled_corners) {
            corners.push_back(cv::Point2f(corner.x / scale, corner.y / scale));
        }

        // 在原图上进行最终的亚像素优化
        cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));
    }

    return found;
}

// 生成3D棋盘格角点
std::vector<cv::Point3f> generateObjectPoints() {
    std::vector<cv::Point3f> corners;
    for (int i = 0; i < board_size.height; ++i) {
        for (int j = 0; j < board_size.width; ++j) {
            corners.push_back(cv::Point3f(j * square_size, i * square_size, 0));
        }
    }
    return corners;
}

// 镜面反射变换：将点关于镜面进行反射
cv::Point3f reflectPointAcrossMirror(const cv::Point3f& point, const cv::Vec4f& mirror_plane) {
    // 镜面方程: ax + by + cz + d = 0
    // 反射公式: P' = P - 2 * ((P·n + d) / |n|²) * n
    float a = mirror_plane[0], b = mirror_plane[1], c = mirror_plane[2], d = mirror_plane[3];
    cv::Vec3f normal(a, b, c);
    float normal_length_sq = normal.dot(normal);

    if (normal_length_sq == 0) return point; // 避免除零

    float distance_to_plane = (point.x * a + point.y * b + point.z * c + d) / normal_length_sq;

    return cv::Point3f(
        point.x - 2 * distance_to_plane * a,
        point.y - 2 * distance_to_plane * b,
        point.z - 2 * distance_to_plane * c
    );
}

// 基于正常视角和镜像视角的位姿差异估计镜面参数
cv::Vec4f estimateMirrorPlane(const std::vector<cv::Mat>& tvecs_normal,
                             const std::vector<cv::Mat>& tvecs_mirror,
                             const std::vector<cv::Mat>& rvecs_normal,
                             const std::vector<cv::Mat>& rvecs_mirror) {
    // 方法：镜面是正常视角和镜像视角中棋盘格位置的中垂面

    std::vector<cv::Point3f> normal_positions, mirror_positions;

    // 收集棋盘格在两个视角下的位置
    for (size_t i = 0; i < std::min(tvecs_normal.size(), tvecs_mirror.size()); ++i) {
        // 正常视角下棋盘格的位置（相机坐标系中）
        cv::Point3f normal_pos(-tvecs_normal[i].at<double>(0),
                               -tvecs_normal[i].at<double>(1),
                               -tvecs_normal[i].at<double>(2));

        // 镜像视角下棋盘格的位置（相机坐标系中）
        cv::Point3f mirror_pos(-tvecs_mirror[i].at<double>(0),
                               -tvecs_mirror[i].at<double>(1),
                               -tvecs_mirror[i].at<double>(2));

        normal_positions.push_back(normal_pos);
        mirror_positions.push_back(mirror_pos);
    }

    // 计算镜面法向量和位置
    cv::Vec3f total_normal(0, 0, 0);
    cv::Point3f total_midpoint(0, 0, 0);

    for (size_t i = 0; i < normal_positions.size(); ++i) {
        // 连接对应点的向量
        cv::Vec3f diff(mirror_positions[i].x - normal_positions[i].x,
                       mirror_positions[i].y - normal_positions[i].y,
                       mirror_positions[i].z - normal_positions[i].z);

        // 中点
        cv::Point3f midpoint((normal_positions[i].x + mirror_positions[i].x) / 2.0f,
                            (normal_positions[i].y + mirror_positions[i].y) / 2.0f,
                            (normal_positions[i].z + mirror_positions[i].z) / 2.0f);

        total_normal += diff;
        total_midpoint.x += midpoint.x;
        total_midpoint.y += midpoint.y;
        total_midpoint.z += midpoint.z;
    }

    // 平均化
    total_normal /= static_cast<float>(normal_positions.size());
    total_midpoint.x /= static_cast<float>(normal_positions.size());
    total_midpoint.y /= static_cast<float>(normal_positions.size());
    total_midpoint.z /= static_cast<float>(normal_positions.size());

    // 归一化法向量
    float norm = sqrt(total_normal[0]*total_normal[0] +
                     total_normal[1]*total_normal[1] +
                     total_normal[2]*total_normal[2]);
    if (norm > 0) {
        total_normal /= norm;
    }

    // 计算d参数：ax + by + cz + d = 0
    float d = -(total_normal[0] * total_midpoint.x +
                total_normal[1] * total_midpoint.y +
                total_normal[2] * total_midpoint.z);

    std::cout << "    估计的镜面法向量: [" << total_normal[0] << ", "
              << total_normal[1] << ", " << total_normal[2] << "]" << std::endl;
    std::cout << "    镜面距离参数 d: " << d << std::endl;

    return cv::Vec4f(total_normal[0], total_normal[1], total_normal[2], d);
}



int main() {
    std::cout << "\n=== 相机镜像视角标定程序 ===" << std::endl;
    std::cout << "棋盘格: " << board_size.width << "x" << board_size.height
              << ", 方格大小: " << square_size << "mm" << std::endl;

    // 步骤1: 加载图像
    std::cout << "\n步骤1: 加载图像..." << std::endl;
    auto normal_files = loadImageFiles(normal_dir);
    auto mirror_files = loadImageFiles(mirror_dir);

    if (normal_files.empty() || mirror_files.empty()) {
        std::cerr << "错误: 无法找到图像文件" << std::endl;
        return -1;
    }

    size_t num_pairs = std::min(normal_files.size(), mirror_files.size());
    std::cout << "找到 " << num_pairs << " 对图像" << std::endl;

    // 步骤2: 检测角点并收集数据
    std::cout << "\n步骤2: 检测棋盘格角点..." << std::endl;
    std::vector<std::vector<cv::Point2f>> normal_image_points, mirror_image_points;
    std::vector<std::vector<cv::Point3f>> object_points;
    auto object_corners = generateObjectPoints();

    for (size_t i = 0; i < num_pairs; ++i) {

        cv::Mat normal_img = cv::imread(normal_files[i]);
        cv::Mat mirror_img = cv::imread(mirror_files[i]);

        if (normal_img.empty() || mirror_img.empty()) {
            std::cout << "  跳过：图像加载失败" << std::endl;
            continue;
        }


        std::vector<cv::Point2f> normal_corners, mirror_corners;

        bool normal_found = detectChessboard(normal_img, normal_corners);
        bool mirror_found = detectChessboard(mirror_img, mirror_corners);


        if (normal_found && mirror_found) {
            // 不对镜像图像进行翻转，保持原始几何关系
            normal_image_points.push_back(normal_corners);
            mirror_image_points.push_back(mirror_corners);
            object_points.push_back(object_corners);

            std::cout << "  ✓ 图像对 " << (i+1) << " 检测成功" << std::endl;
        } else {
            std::cout << "  ✗ 图像对 " << (i+1) << " 检测失败" << std::endl;
        }
    }

    if (normal_image_points.size() < 3) {
        std::cerr << "错误: 需要至少3对有效图像进行标定" << std::endl;
        return -1;
    }

    std::cout << "成功检测 " << normal_image_points.size() << " 对图像的角点" << std::endl;



    // 获取图像尺寸
    cv::Size image_size(6000, 8000);  // 原始图像尺寸

    // 步骤3: 使用PnP方法进行镜像几何标定
    std::cout << "\n步骤3: 使用PnP方法进行镜像几何标定..." << std::endl;

    // 步骤3.1: 标定正常视角相机内参
    std::cout << "  步骤3.1: 标定正常视角相机内参..." << std::endl;
    cv::Mat camera_matrix, dist_coeffs;
    std::vector<cv::Mat> rvecs_normal, tvecs_normal;

    double rms_normal = cv::calibrateCamera(
        object_points,
        normal_image_points,
        image_size,
        camera_matrix,
        dist_coeffs,
        rvecs_normal,
        tvecs_normal,
        cv::CALIB_RATIONAL_MODEL
    );

    std::cout << "    正常视角标定完成，RMS误差: " << rms_normal << std::endl;

    // 步骤3.2: 直接计算镜像视角位姿（使用相同的3D棋盘格点）
    std::cout << "  步骤3.2: 计算镜像视角位姿..." << std::endl;
    std::vector<cv::Mat> rvecs_mirror, tvecs_mirror;

    for (size_t i = 0; i < mirror_image_points.size(); ++i) {
        cv::Mat rvec, tvec;
        bool success = cv::solvePnP(object_points[i], mirror_image_points[i],
                                   camera_matrix, dist_coeffs, rvec, tvec);
        if (success) {
            rvecs_mirror.push_back(rvec.clone());
            tvecs_mirror.push_back(tvec.clone());
        }
    }

    std::cout << "    成功计算 " << rvecs_mirror.size() << " 个镜像视角位姿" << std::endl;

    // 步骤3.3: 计算从镜像视角到正常视角的变换
    std::cout << "  步骤3.3: 计算镜像到正常视角的变换..." << std::endl;

    std::vector<cv::Mat> relative_R, relative_T;
    double total_error = 0.0;

    for (size_t i = 0; i < std::min(rvecs_normal.size(), rvecs_mirror.size()); ++i) {
        // 将旋转向量转换为旋转矩阵
        cv::Mat R_normal, R_mirror;
        cv::Rodrigues(rvecs_normal[i], R_normal);
        cv::Rodrigues(rvecs_mirror[i], R_mirror);

        // 计算相对变换: T_normal = T_rel * T_mirror
        // 因此: T_rel = T_normal * T_mirror^(-1)
        cv::Mat R_rel = R_normal * R_mirror.t();
        cv::Mat T_rel = tvecs_normal[i] - R_rel * tvecs_mirror[i];

        relative_R.push_back(R_rel.clone());
        relative_T.push_back(T_rel.clone());

        // 计算重投影误差验证
        std::vector<cv::Point2f> projected_points;
        cv::Mat rvec_rel;
        cv::Rodrigues(R_rel, rvec_rel);
        cv::projectPoints(object_points[i], rvec_rel, T_rel, camera_matrix, dist_coeffs, projected_points);

        double error = 0.0;
        for (size_t j = 0; j < std::min(normal_image_points[i].size(), projected_points.size()); ++j) {
            cv::Point2f diff = normal_image_points[i][j] - projected_points[j];
            error += sqrt(diff.x * diff.x + diff.y * diff.y);
        }
        total_error += error / normal_image_points[i].size();
    }

    // 计算平均变换
    cv::Mat R_avg = cv::Mat::zeros(3, 3, CV_64F);
    cv::Mat T_avg = cv::Mat::zeros(3, 1, CV_64F);

    for (size_t i = 0; i < relative_R.size(); ++i) {
        R_avg += relative_R[i];
        T_avg += relative_T[i];
    }

    R_avg /= static_cast<double>(relative_R.size());
    T_avg /= static_cast<double>(relative_T.size());

    // 重新正交化旋转矩阵
    cv::Mat U, S, Vt;
    cv::SVD::compute(R_avg, S, U, Vt);
    cv::Mat R = U * Vt;
    cv::Mat T = T_avg.clone();

    double avg_error = total_error / relative_R.size();

    std::cout << "    平均重投影误差: " << avg_error << " 像素" << std::endl;

    // 步骤4: 验证和显示结果
    std::cout << "\n步骤4: 验证标定结果..." << std::endl;

    // 显示相机内参标定结果
    std::cout << "\n=== 相机内参标定结果 ===" << std::endl;
    std::cout << "相机内参矩阵 (RMS=" << rms_normal << "):" << std::endl << camera_matrix << std::endl;
    std::cout << "畸变系数:" << std::endl << dist_coeffs.t() << std::endl;

    // 显示镜像几何标定结果
    std::cout << "\n=== 镜像几何标定结果 ===" << std::endl;
    std::cout << "旋转矩阵 R (镜像->正常):" << std::endl << R << std::endl;
    std::cout << "平移向量 T (镜像->正常):" << std::endl << T << std::endl;
    std::cout << "平均重投影误差: " << avg_error << " 像素" << std::endl;

    // 计算旋转角度和平移距离
    cv::Mat rvec;
    cv::Rodrigues(R, rvec);
    double angle = cv::norm(rvec) * 180.0 / CV_PI;
    double distance = cv::norm(T);

    std::cout << "旋转角度: " << angle << " 度" << std::endl;
    std::cout << "平移距离: " << distance << " mm" << std::endl;

    // 步骤5: 可视化投影偏差
    std::cout << "\n步骤5: 可视化投影偏差..." << std::endl;

    for (size_t i = 0; i < normal_image_points.size(); ++i) {
        // 加载原始图像用于可视化
        cv::Mat normal_img = cv::imread(normal_files[i]);
        cv::Mat mirror_img = cv::imread(mirror_files[i]);

        if (normal_img.empty() || mirror_img.empty()) continue;

        // 缩放图像用于显示
        cv::Mat normal_display, mirror_display;
        double display_scale = 0.2;  // 缩放到20%用于显示
        cv::resize(normal_img, normal_display, cv::Size(), display_scale, display_scale);
        cv::resize(mirror_img, mirror_display, cv::Size(), display_scale, display_scale);

        // 缩放角点坐标
        std::vector<cv::Point2f> normal_corners_display, mirror_corners_display;
        for (const auto& pt : normal_image_points[i]) {
            normal_corners_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }
        for (const auto& pt : mirror_image_points[i]) {
            mirror_corners_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }

        // 方法1: 直接使用镜像视角的相机位姿投影到正常视角
        // 首先计算镜像视角相对于棋盘格的位姿
        cv::Mat rvec_mirror, tvec_mirror;
        cv::solvePnP(object_points[i], mirror_image_points[i],
                    camera_matrix, dist_coeffs, rvec_mirror, tvec_mirror);

        // 将镜像视角的位姿变换到正常视角坐标系
        cv::Mat R_mirror;
        cv::Rodrigues(rvec_mirror, R_mirror);
        cv::Mat R_transformed = R * R_mirror;
        cv::Mat t_transformed = R * tvec_mirror + T;
        cv::Mat rvec_transformed;
        cv::Rodrigues(R_transformed, rvec_transformed);

        // 使用变换后的位姿将3D点投影到正常视角图像
        std::vector<cv::Point2f> projected_points;
        cv::projectPoints(object_points[i], rvec_transformed, t_transformed,
                         camera_matrix, dist_coeffs, projected_points);
        // 方法2: 使用已估计的镜面几何模型
        // 使用之前估计的镜面参数

        // 计算正常视角相对于棋盘格的位姿
        cv::Mat rvec_normal, tvec_normal;
        cv::solvePnP(object_points[i], normal_image_points[i],
                    camera_matrix, dist_coeffs, rvec_normal, tvec_normal);

        // 使用相同的3D点进行对比投影
        std::vector<cv::Point2f> reflected_projected_points;
        cv::projectPoints(object_points[i], rvec_normal, tvec_normal,
                         camera_matrix, dist_coeffs, reflected_projected_points);

        // 缩放投影点
        std::vector<cv::Point2f> projected_points_display;
        for (const auto& pt : projected_points) {
            projected_points_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }

        std::vector<cv::Point2f> reflected_projected_display;
        for (const auto& pt : reflected_projected_points) {
            reflected_projected_display.push_back(cv::Point2f(pt.x * display_scale, pt.y * display_scale));
        }


        // 在正常视角图像上绘制
        cv::Mat result_img = normal_display.clone();

        // 绘制正常视角检测到的角点（绿色圆圈）
        for (const auto& pt : normal_corners_display) {
            cv::circle(result_img, pt, 8, cv::Scalar(0, 255, 0), 2);
        }

        // 绘制从镜像视角投影过来的点（红色十字）
        for (const auto& pt : projected_points_display) {
            cv::drawMarker(result_img, pt, cv::Scalar(0, 0, 255), cv::MARKER_CROSS, 16, 2);
        }

        // 绘制连接线显示偏差（蓝色线）
        for (size_t j = 0; j < std::min(normal_corners_display.size(), projected_points_display.size()); ++j) {
            cv::line(result_img, normal_corners_display[j], projected_points_display[j],
                    cv::Scalar(255, 0, 0), 1);
        }

        // 计算平均偏差
        double total_error = 0.0;
        for (size_t j = 0; j < std::min(normal_corners_display.size(), projected_points_display.size()); ++j) {
            cv::Point2f diff = normal_corners_display[j] - projected_points_display[j];
            total_error += sqrt(diff.x * diff.x + diff.y * diff.y);
        }
        double avg_error = total_error / std::min(normal_corners_display.size(), projected_points_display.size());

        // 添加图例和信息
        cv::putText(result_img, "Green: Normal view corners", cv::Point(10, 30),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
        cv::putText(result_img, "Red: Projected from mirror", cv::Point(10, 60),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 255), 2);
        cv::putText(result_img, "Blue: Error lines", cv::Point(10, 90),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 0, 0), 2);
        cv::putText(result_img, "Avg error: " + std::to_string(avg_error / display_scale) + " pixels",
                   cv::Point(10, 120), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);

        std::cout << "  图像 " << (i + 1) << " 平均投影误差: " << (avg_error / display_scale) << " 像素" << std::endl;
        // 显示结果
        std::string window_name = "Projection Error Visualization - Image ";
        cv::namedWindow(window_name, cv::WINDOW_NORMAL);
        cv::imshow(window_name, result_img);
        cv::waitKey(0);
    }

    // 精度评估
    if (avg_error < 1.0) {
        std::cout << "✓ 标定精度优秀" << std::endl;
    } else if (avg_error < 2.0) {
        std::cout << "✓ 标定精度良好" << std::endl;
    } else {
        std::cout << "⚠ 标定精度一般，建议重新标定" << std::endl;
    }


    return 0;
}