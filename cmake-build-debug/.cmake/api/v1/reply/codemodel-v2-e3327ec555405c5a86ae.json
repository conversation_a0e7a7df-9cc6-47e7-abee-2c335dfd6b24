{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "camera_calibrate", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "camera_calibrate::@6890427a1f51a3e7e1df", "jsonFile": "target-camera_calibrate-Debug-cc4835c2203dc72d36ef.json", "name": "camera_calibrate", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug", "source": "/Users/<USER>/work/Proj/camera_calibrate"}, "version": {"major": 2, "minor": 7}}