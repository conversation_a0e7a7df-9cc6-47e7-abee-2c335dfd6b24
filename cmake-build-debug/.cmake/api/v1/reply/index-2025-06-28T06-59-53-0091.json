{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake", "cpack": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack", "ctest": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest", "root": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-e3327ec555405c5a86ae.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-563c2f0e1983f5c48145.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-74894f7bd1d547ff10ad.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-96ce99ee7930729e1e18.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-563c2f0e1983f5c48145.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-74894f7bd1d547ff10ad.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e3327ec555405c5a86ae.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-96ce99ee7930729e1e18.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}