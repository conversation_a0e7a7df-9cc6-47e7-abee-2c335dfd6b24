{"artifacts": [{"path": "camera_calibrate"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++20 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk -fcolor-diagnostics"}], "includes": [{"backtrace": 2, "isSystem": true, "path": "/usr/local/opencv_4_8_1/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "20"}, "sourceIndexes": [0]}], "id": "camera_calibrate::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/usr/local/opencv_4_8_1/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_img_hash.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib", "role": "libraries"}], "language": "CXX"}, "name": "camera_calibrate", "nameOnDisk": "camera_calibrate", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}