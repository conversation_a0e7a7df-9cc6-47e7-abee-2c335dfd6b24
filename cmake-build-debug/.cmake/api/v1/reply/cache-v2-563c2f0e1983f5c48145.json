{"entries": [{"name": "Boost_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Boost."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Boost-1.85.0"}, {"name": "Boost_FILESYSTEM_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/opt/homebrew/lib/libboost_filesystem-mt.dylib"}, {"name": "Boost_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/opt/homebrew/include"}, {"name": "Boost_IOSTREAMS_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/opt/homebrew/lib/libboost_iostreams-mt.dylib"}, {"name": "Boost_SERIALIZATION_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/opt/homebrew/lib/libboost_serialization-mt.dylib"}, {"name": "Boost_SYSTEM_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/opt/homebrew/lib/libboost_system-mt.dylib"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_ADDR2LINE-NOTFOUND"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "6"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COLOR_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "MACHO"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Unix Makefiles"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/Users/<USER>/work/Proj/camera_calibrate"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_NAME_TOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/install_name_tool"}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/make"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_OBJCOPY-NOTFOUND"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump"}, {"name": "CMAKE_OSX_ARCHITECTURES", "properties": [{"name": "HELPSTRING", "value": "Build architectures for OSX"}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_DEPLOYMENT_TARGET", "properties": [{"name": "HELPSTRING", "value": "Minimum OS X version to target for deployment (at runtime); newer APIs weak linked. Set to empty string for default value."}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_SYSROOT", "properties": [{"name": "HELPSTRING", "value": "The product will be built against the headers and libraries located inside the indicated SDK."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "camera_calibrate"}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_READELF-NOTFOUND"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "Eigen3_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Eigen3."}], "type": "PATH", "value": "/opt/homebrew/share/eigen3/cmake"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Boost", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/opt/homebrew/lib/cmake/Boost-1.85.0/BoostConfig.cmake][cfound components: system iostreams filesystem serialization ][v1.85.0(1.65.0)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCV", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCV"}], "type": "INTERNAL", "value": "[/usr/local/opencv_4_8_1][v4.8.1()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenGL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenGL"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework][cfound components: OpenGL ][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL"}], "type": "INTERNAL", "value": "[pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_outofcore;pcl_people;Boost::system;Boost::iostreams;Boost::filesystem;Boost::serialization;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::RenderingContextOpenGL2;VTK::GUISupportQt;FLANN::FLANN;QHULL::QHULL][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14;/opt/homebrew/include/eigen3;/opt/homebrew/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_2D", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_2D"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_COMMON", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_COMMON"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_FEATURES", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_FEATURES"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_FILTERS", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_FILTERS"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_GEOMETRY", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_GEOMETRY"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_IO", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_IO"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_KDTREE", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_KDTREE"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_KEYPOINTS", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_KEYPOINTS"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_ML", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_ML"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_OCTREE", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_OCTREE"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_OUTOFCORE", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_OUTOFCORE"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_PEOPLE", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_PEOPLE"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_RECOGNITION", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_RECOGNITION"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_REGISTRATION", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_REGISTRATION"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_SAMPLE_CONSENSUS", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_SAMPLE_CONSENSUS"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEARCH", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_SEARCH"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEGMENTATION", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_SEGMENTATION"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_STEREO", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_STEREO"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_SURFACE", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_SURFACE"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_TRACKING", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_TRACKING"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PCL_VISUALIZATION", "properties": [{"name": "HELPSTRING", "value": "Details about finding PCL_VISUALIZATION"}], "type": "INTERNAL", "value": "[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PNG", "properties": [{"name": "HELPSTRING", "value": "Details about finding PNG"}], "type": "INTERNAL", "value": "[/opt/homebrew/lib/libpng.dylib][/opt/homebrew/include][v1.6.43()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Pcap", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libpcap.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON><PERSON><PERSON><PERSON>"}], "type": "INTERNAL", "value": "[1][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapOpenGL", "properties": [{"name": "HELPSTRING", "value": "Details about finding WrapOpenGL"}], "type": "INTERNAL", "value": "[ON][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZLIB", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZLIB"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][c ][v1.2.12()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_libusb", "properties": [{"name": "HELPSTRING", "value": "Details about finding libusb"}], "type": "INTERNAL", "value": "[/opt/homebrew/lib/libusb-1.0.dylib][/opt/homebrew/include][v()]"}, {"name": "HAVE_STDATOMIC", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STDATOMIC"}], "type": "INTERNAL", "value": "1"}, {"name": "MACDEPLOYQT_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/opt/homebrew/bin/macdeployqt"}, {"name": "OPENGL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Include for OpenGL on OS X"}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENGL_gl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "OpenGL library for OS X"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENGL_glu_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "GLU library for OS X (usually same as OpenGL library)"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "/usr/local/opencv_4_8_1/lib/cmake/opencv4"}, {"name": "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "OpenMP_COMPILE_RESULT_C_Xclang fopenmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "OpenMP_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_CXX_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_C_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_libomp_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "OpenMP_libomp_LIBRARY-NOTFOUND"}, {"name": "PCAP_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include"}, {"name": "PCAP_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libpcap.tbd"}, {"name": "PCL_2D_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to 2d headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_COMMON_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to common headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_COMMON_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_common library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib"}, {"name": "PCL_COMMON_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_common library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib"}, {"name": "PCL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for PCL."}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/share/pcl-1.14"}, {"name": "PCL_FEATURES_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to features headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_FEATURES_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_features library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib"}, {"name": "PCL_FEATURES_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_features library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib"}, {"name": "PCL_FILTERS_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to filters headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_FILTERS_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_filters library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib"}, {"name": "PCL_FILTERS_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_filters library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib"}, {"name": "PCL_GEOMETRY_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to geometry headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_IO_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to io headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_IO_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_io library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib"}, {"name": "PCL_IO_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_io library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib"}, {"name": "PCL_KDTREE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to kdtree headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_KDTREE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_kdtree library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib"}, {"name": "PCL_KDTREE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_kdtree library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib"}, {"name": "PCL_KEYPOINTS_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to keypoints headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_KEYPOINTS_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_keypoints library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib"}, {"name": "PCL_KEYPOINTS_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_keypoints library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib"}, {"name": "PCL_ML_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to ml headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_ML_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_ml library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib"}, {"name": "PCL_ML_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_ml library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib"}, {"name": "PCL_OCTREE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to octree headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_OCTREE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_octree library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib"}, {"name": "PCL_OCTREE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_octree library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib"}, {"name": "PCL_OUTOFCORE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to outofcore headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_OUTOFCORE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_outofcore library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib"}, {"name": "PCL_OUTOFCORE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_outofcore library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib"}, {"name": "PCL_PEOPLE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to people headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_PEOPLE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_people library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib"}, {"name": "PCL_PEOPLE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_people library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib"}, {"name": "PCL_RECOGNITION_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to recognition headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_RECOGNITION_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_recognition library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib"}, {"name": "PCL_RECOGNITION_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_recognition library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib"}, {"name": "PCL_REGISTRATION_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to registration headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_REGISTRATION_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_registration library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib"}, {"name": "PCL_REGISTRATION_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_registration library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib"}, {"name": "PCL_SAMPLE_CONSENSUS_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to sample_consensus headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_SAMPLE_CONSENSUS_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_sample_consensus library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib"}, {"name": "PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_sample_consensus library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib"}, {"name": "PCL_SEARCH_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to search headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_SEARCH_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_search library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib"}, {"name": "PCL_SEARCH_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_search library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib"}, {"name": "PCL_SEGMENTATION_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to segmentation headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_SEGMENTATION_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_segmentation library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib"}, {"name": "PCL_SEGMENTATION_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_segmentation library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib"}, {"name": "PCL_STEREO_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to stereo headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_STEREO_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_stereo library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib"}, {"name": "PCL_STEREO_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_stereo library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib"}, {"name": "PCL_SURFACE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to surface headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_SURFACE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_surface library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib"}, {"name": "PCL_SURFACE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_surface library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib"}, {"name": "PCL_TRACKING_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to tracking headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_TRACKING_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_tracking library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib"}, {"name": "PCL_TRACKING_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_tracking library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib"}, {"name": "PCL_VISUALIZATION_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to visualization headers"}], "type": "PATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14"}, {"name": "PCL_VISUALIZATION_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_visualization library"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib"}, {"name": "PCL_VISUALIZATION_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "path to pcl_visualization library debug"}], "type": "FILEPATH", "value": "/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib"}, {"name": "PC_libusb_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0"}, {"name": "PC_libusb_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_libusb_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/include"}, {"name": "PC_libusb_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0"}, {"name": "PC_libusb_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/opt/homebrew/Cellar/libusb/1.0.26/lib;-lusb-1.0"}, {"name": "PC_libusb_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/lib"}, {"name": "PC_libusb_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "usb-1.0"}, {"name": "PC_libusb_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/lib"}, {"name": "PC_libusb_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libusb-1.0"}, {"name": "PC_libusb_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26"}, {"name": "PC_libusb_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0"}, {"name": "PC_libusb_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0"}, {"name": "PC_libusb_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/opt/homebrew/Cellar/libusb/1.0.26/lib;-lusb-1.0;-lobjc;-Wl,-framework,IOKit;-Wl,-framework,CoreFoundation;-Wl,-framework,Security"}, {"name": "PC_libusb_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-Wl,-framework,IOKit;-Wl,-framework,CoreFoundation;-Wl,-framework,Security"}, {"name": "PC_libusb_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "usb-1.0;objc"}, {"name": "PC_libusb_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/lib"}, {"name": "PC_libusb_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1.0.26"}, {"name": "PC_libusb_libusb-1.0_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_libusb-1.0_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_libusb-1.0_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_libusb_libusb-1.0_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/opt/homebrew/bin/pkg-config"}, {"name": "PNG_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "PNG_LIBRARY_DEBUG-NOTFOUND"}, {"name": "PNG_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/homebrew/lib/libpng.dylib"}, {"name": "PNG_PNG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/opt/homebrew/include"}, {"name": "QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) host Qt components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_ADDITIONAL_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_FEATURE_abstractbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: abstractbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_abstractslider", "properties": [{"name": "HELPSTRING", "value": "Qt feature: abstractslider (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_accessibility", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_accessibility_atspi_bridge", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_action", "properties": [{"name": "HELPSTRING", "value": "Qt feature: action (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_aesni", "properties": [{"name": "HELPSTRING", "value": "Qt feature: aesni (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_alloca", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alloca_h", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca_h (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alloca_malloc_h", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca_malloc_h (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_android_style_assets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: android_style_assets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_animation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: animation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_appstore_compliant", "properties": [{"name": "HELPSTRING", "value": "Qt feature: appstore_compliant (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_crc32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crc32 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_arm_crypto", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crypto (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512bw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512bw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512cd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512cd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512dq", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512dq (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512er", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512er (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512f", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512f (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512ifma", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512ifma (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512pf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512pf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512vbmi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512vbmi2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx512vl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_backtrace", "properties": [{"name": "HELPSTRING", "value": "Qt feature: backtrace (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_buttongroup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: buttongroup (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_calendarwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: calendarwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cborstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cborstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_checkbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: checkbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clipboard", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clipboard (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clock_gettime", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_gettime (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clock_monotonic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_monotonic (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_close_range", "properties": [{"name": "HELPSTRING", "value": "Qt feature: close_range (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_colordialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: colordialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_colornames", "properties": [{"name": "HELPSTRING", "value": "Qt feature: colornames (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_columnview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: columnview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_combobox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: combobox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_commandlineparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: commandlineparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_commandlinkbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: commandlinkbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_completer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: completer (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concatenatetablesproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concatenatetablesproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concurrent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concurrent (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_contextmenu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: contextmenu (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cpp_winrt", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cpp_winrt (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cross_compile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cross_compile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cssparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cssparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ctf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ctf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cursor", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cursor (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx11_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx11_future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx17_filesystem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx17_filesystem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx20", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx20 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2a", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2a (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2b", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2b (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_datawidgetmapper", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datawidgetmapper (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datestring", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datestring (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datetimeedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datetimeedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datetimeparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datetimeparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_debug", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_debug_and_release", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug_and_release (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_desktopservices", "properties": [{"name": "HELPSTRING", "value": "Qt feature: desktopservices (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_developer_build", "properties": [{"name": "HELPSTRING", "value": "Qt feature: developer_build (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dial", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dial (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dialogbuttonbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dialogbuttonbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_direct2d", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_direct2d1_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d1_1 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directwrite", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directwrite3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dladdr", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dladdr (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dlopen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dlopen (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dockwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dockwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_draganddrop", "properties": [{"name": "HELPSTRING", "value": "Qt feature: draganddrop (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_drm_atomic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: drm_atomic (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dynamicgl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dynamicgl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_easingcurve", "properties": [{"name": "HELPSTRING", "value": "Qt feature: easingcurve (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_effects", "properties": [{"name": "HELPSTRING", "value": "Qt feature: effects (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_egl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_egl_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_brcm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_brcm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_egldevice", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_egldevice (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_gbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_gbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_mali", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_mali (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_openwfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_openwfd (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_rcar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_rcar (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv_wl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv_wl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_errormessage", "properties": [{"name": "HELPSTRING", "value": "Qt feature: errormessage (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_etw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: etw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_evdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: evdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_f16c", "properties": [{"name": "HELPSTRING", "value": "Qt feature: f16c (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_filedialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filedialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemiterator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemiterator (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemwatcher", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemwatcher (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontcombobox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontcombobox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontconfig", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontconfig (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_fontdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_force_asserts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_asserts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_forkfd_pidfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: forkfd_pidfd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_formlayout", "properties": [{"name": "HELPSTRING", "value": "Qt feature: formlayout (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_framework", "properties": [{"name": "HELPSTRING", "value": "Qt feature: framework (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fscompleter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fscompleter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_futimens", "properties": [{"name": "HELPSTRING", "value": "Qt feature: futimens (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gc_binaries", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gc_binaries (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gestures", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gestures (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_getauxval", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getauxval (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_getentropy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getentropy (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gif", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gif (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_glib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: glib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_graphicseffect", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicseffect (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_graphicsframecapture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicsframecapture (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_graphicsview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicsview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_groupbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: groupbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gtk3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gtk3 (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gui", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gui (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: harfbuzz (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_highdpiscaling", "properties": [{"name": "HELPSTRING", "value": "Qt feature: highdpiscaling (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_hij<PERSON>endar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: hij<PERSON><PERSON>ar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ico", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ico (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_icu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: icu (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_identityproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: identityproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_im", "properties": [{"name": "HELPSTRING", "value": "Qt feature: im (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_heuristic_mask", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_heuristic_mask (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_text", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_text (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_bmp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_bmp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_ppm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_ppm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xpm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xpm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformatplugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformatplugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageio_text_loading", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageio_text_loading (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_inotify", "properties": [{"name": "HELPSTRING", "value": "Qt feature: inotify (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_inputdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: inputdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_integrityfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: integrityfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_integrityhid", "properties": [{"name": "HELPSTRING", "value": "Qt feature: <PERSON>hid (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_intelcet", "properties": [{"name": "HELPSTRING", "value": "Qt feature: intelcet (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_islamiccivilcalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: islamiccivilcalendar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemviews", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemviews (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_jalalicalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jala<PERSON><PERSON>dar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_journald", "properties": [{"name": "HELPSTRING", "value": "Qt feature: journald (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_keysequenceedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: keysequenceedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_kms", "properties": [{"name": "HELPSTRING", "value": "Qt feature: kms (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_label", "properties": [{"name": "HELPSTRING", "value": "Qt feature: label (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_largefile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: largefile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lcdnumber", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lcdnumber (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_axis_api", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_axis_api (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_hires_wheel_support", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_library", "properties": [{"name": "HELPSTRING", "value": "Qt feature: library (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libudev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libudev (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_lineedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lineedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linkat", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linkat (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linuxfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linuxfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_listview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: listview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_listwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: listwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lttng", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lttng (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mainwindow", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mainwindow (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mdiarea", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mdiarea (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_menu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: menu (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_menubar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: menubar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_messagebox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: messagebox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mimetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mimetype_database", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype_database (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mips_dsp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dsp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mips_dspr2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dspr2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_movie", "properties": [{"name": "HELPSTRING", "value": "Qt feature: movie (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mtdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mtdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_multiprocess", "properties": [{"name": "HELPSTRING", "value": "Qt feature: multiprocess (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_neon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: neon (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_network", "properties": [{"name": "HELPSTRING", "value": "Qt feature: network (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_no_direct_extern_access", "properties": [{"name": "HELPSTRING", "value": "Qt feature: no_direct_extern_access (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_opengles2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles31", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles31 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles32 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_openssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openssl_hash", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl_hash (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_openssl_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv11 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv30", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv30 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openvg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openvg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pdf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pdf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_permissions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: permissions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_picture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: picture (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pkg_config", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pkg_config (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_plugin_manifest", "properties": [{"name": "HELPSTRING", "value": "Qt feature: plugin_manifest (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: png (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_poll_exit_on_error", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_exit_on_error (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_poll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_poll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_poll_pollts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_pollts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_ppoll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_ppoll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_select", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_select (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_posix_fallocate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_fallocate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_posix_sem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_sem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_posix_shm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_shm (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_precompile_header", "properties": [{"name": "HELPSTRING", "value": "Qt feature: precompile_header (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_printsupport", "properties": [{"name": "HELPSTRING", "value": "Qt feature: printsupport (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_private_tests", "properties": [{"name": "HELPSTRING", "value": "Qt feature: private_tests (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_process", "properties": [{"name": "HELPSTRING", "value": "Qt feature: process (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_processenvironment", "properties": [{"name": "HELPSTRING", "value": "Qt feature: processenvironment (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_progressbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: progressbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_progressdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: progressdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_proxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: proxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pushbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pushbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qqnx_imf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_imf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qqnx_pps", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_pps (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qt_framework", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qt_framework (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_radiobutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: radiobutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_64bit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_64bit (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_fp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_fp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rdrnd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdrnd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_rdseed", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdseed (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_reduce_exports", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_exports (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_reduce_relocations", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_relocations (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_regularexpression", "properties": [{"name": "HELPSTRING", "value": "Qt feature: regularexpression (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_relocatable", "properties": [{"name": "HELPSTRING", "value": "Qt feature: relocatable (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_renameat2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: renameat2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_resizehandler", "properties": [{"name": "HELPSTRING", "value": "Qt feature: resizehandler (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rpath", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rpath (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rubberband", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rubberband (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_scrollarea", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scrollarea (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_scrollbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scrollbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_scroller", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scroller (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_separate_debug_info", "properties": [{"name": "HELPSTRING", "value": "Qt feature: separate_debug_info (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sessionmanager", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sessionmanager (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_settings", "properties": [{"name": "HELPSTRING", "value": "Qt feature: settings (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sha3_fast", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sha3_fast (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shani", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shani (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_shared", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shared (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sharedmemory", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sharedmemory (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shortcut", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shortcut (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_signaling_nan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: signaling_nan (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_simulator_and_device", "properties": [{"name": "HELPSTRING", "value": "Qt feature: simulator_and_device (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sizegrip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sizegrip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_slider", "properties": [{"name": "HELPSTRING", "value": "Qt feature: slider (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_slog2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: slog2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sortfilterproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sortfilterproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spinbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spinbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_splashscreen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: splashscreen (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_splitter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: splitter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sql", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sql (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sse4_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_1 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sse4_2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_ssse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ssse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stack_protector_strong", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stack_protector_strong (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stackedwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stackedwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_standarditemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: standarditemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_static", "properties": [{"name": "HELPSTRING", "value": "Qt feature: static (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_statusbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: statusbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_statustip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: statustip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_std_atomic64", "properties": [{"name": "HELPSTRING", "value": "Qt feature: std_atomic64 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stdlib_libcpp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stdlib_libcpp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stringlistmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stringlistmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_android", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_android (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_style_fusion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_fusion (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_mac", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_mac (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_stylesheet", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_stylesheet (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_windows", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_windows (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_windows11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_windows11 (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_style_windowsvista", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_windowsvista (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_syntaxhighlighter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: syntaxhighlighter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_syslog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: syslog (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_harfbuzz (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_libb2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_libb2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_xcb_xinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_xcb_xinput (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_zlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_zlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_systemsemaphore", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemsemaphore (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_systemtrayicon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemtrayicon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sysv_sem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sysv_sem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sysv_shm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sysv_shm (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabletevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabletevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tableview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tableview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tablewidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tablewidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_temporaryfile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: temporaryfile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_testlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: testlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textbrowser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textbrowser (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textdate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textdate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_texthtmlparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: texthtmlparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textodfwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textodfwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_thread", "properties": [{"name": "HELPSTRING", "value": "Qt feature: thread (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_timezone", "properties": [{"name": "HELPSTRING", "value": "Qt feature: timezone (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tooltip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tooltip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_translation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: translation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_transposeproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: transposeproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_treeview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: treeview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_treewidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: treewidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tslib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tslib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_tuiotouch", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tui<PERSON>uch (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undocommand", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undocommand (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undogroup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undogroup (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undostack", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undostack (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undoview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undoview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_use_bfd_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_bfd_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_gold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_gold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_lld_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_lld_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_mold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_mold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vaes", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vaes (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_validator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: validator (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vkgen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vkgen (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vkkhrdisplay", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vk<PERSON>rdisplay (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vnc", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vnc (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vulkan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vulkan (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wasm_exceptions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_exceptions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_simd128", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_simd128 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wayland", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wayland (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_whatsthis", "properties": [{"name": "HELPSTRING", "value": "Qt feature: whatsthis (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wheelevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wheelevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_widgets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: widgets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_widgettextcontrol", "properties": [{"name": "HELPSTRING", "value": "Qt feature: widgettextcontrol (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wizard", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wizard (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_x86intrin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: x86intrin (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_egl_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_egl_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_glx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_glx_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_native_painting", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_native_painting (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_sm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_sm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xkbcommon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xkbcommon_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xml", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xml (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstream", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstream (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xrender", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xrender (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_zstd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: zstd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "Qhull_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qhull."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qhull"}, {"name": "Qt6CoreTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6CoreTools."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6CoreTools"}, {"name": "Qt6Core_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Core."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6Core"}, {"name": "Qt6DBusTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6DBusTools."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6DBusTools"}, {"name": "Qt6DBus_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6DBus."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6DBus"}, {"name": "Qt6GuiTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6GuiTools."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6GuiTools"}, {"name": "Qt6Gui_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Gui."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6Gui"}, {"name": "Qt6OpenGLWidgets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6OpenGLWidgets."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6OpenGLWidgets"}, {"name": "Qt6OpenGL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6OpenGL."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6OpenGL"}, {"name": "Qt6WidgetsTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6WidgetsTools."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6WidgetsTools"}, {"name": "Qt6Widgets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Widgets."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6Widgets"}, {"name": "Qt6_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/Qt6"}, {"name": "VTK_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing VTKConfig.cmake"}], "type": "PATH", "value": "/usr/local/lib/cmake/vtk-9.3"}, {"name": "VTK_MPI_NUMPROCS", "properties": [{"name": "HELPSTRING", "value": "Number of processors available to run parallel tests."}], "type": "INTERNAL", "value": "2"}, {"name": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_GLSLC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLC_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "Vulkan_INCLUDE_DIR-NOTFOUND"}, {"name": "Vulkan_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "Vulkan_LIBRARY-NOTFOUND"}, {"name": "WrapOpenGL_AGL", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/AGL.framework"}, {"name": "ZLIB_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include"}, {"name": "ZLIB_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "ZLIB_LIBRARY_DEBUG-NOTFOUND"}, {"name": "ZLIB_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libz.tbd"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "__pkg_config_arguments_PC_libusb", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libusb-1.0"}, {"name": "__pkg_config_checked_PC_libusb", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "boost_atomic_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_atomic."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_atomic-1.85.0"}, {"name": "boost_filesystem_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_filesystem."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_filesystem-1.85.0"}, {"name": "boost_headers_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_headers."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_headers-1.85.0"}, {"name": "boost_iostreams_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_iostreams."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_iostreams-1.85.0"}, {"name": "boost_serialization_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_serialization."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_serialization-1.85.0"}, {"name": "boost_system_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_system."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/boost_system-1.85.0"}, {"name": "camera_calibrate_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug"}, {"name": "camera_calibrate_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "camera_calibrate_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/Proj/camera_calibrate"}, {"name": "flann_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for flann."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/flann"}, {"name": "libusb_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/opt/homebrew/include"}, {"name": "libusb_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/homebrew/lib/libusb-1.0.dylib"}, {"name": "pkgcfg_lib_PC_libusb_usb-1.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/homebrew/Cellar/libusb/1.0.26/lib/libusb-1.0.dylib"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/libusb/1.0.26/lib"}], "kind": "cache", "version": {"major": 2, "minor": 0}}