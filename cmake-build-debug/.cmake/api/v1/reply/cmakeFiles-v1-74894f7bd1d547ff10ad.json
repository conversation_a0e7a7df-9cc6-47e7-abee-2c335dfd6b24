{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isExternal": true, "path": "/usr/local/opencv_4_8_1/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/usr/local/opencv_4_8_1/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/local/opencv_4_8_1/lib/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/usr/local/opencv_4_8_1/lib/cmake/opencv4/OpenCVModules-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug", "source": "/Users/<USER>/work/Proj/camera_calibrate"}, "version": {"major": 1, "minor": 1}}