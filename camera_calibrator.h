#ifndef CAMERA_CALIBRATOR_H
#define CAMERA_CALIBRATOR_H

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>

struct CalibrationData {
    // 相机内参
    cv::Mat camera_matrix;
    cv::Mat dist_coeffs;
    
    // 标定结果：从侧面镜像视角到中间正常视角的变换
    cv::Mat R_mirror_to_normal;  // 旋转矩阵
    cv::Mat T_mirror_to_normal;  // 平移向量
    
    // 标定精度
    double reprojection_error;
    
    // 棋盘格参数
    cv::Size board_size;
    float square_size;
    
    CalibrationData() : reprojection_error(0.0), square_size(1.0) {}
};

struct ImagePair {
    cv::Mat normal_image;    // 中间正常视角图像
    cv::Mat mirror_image;    // 侧面镜像视角图像
    std::string normal_path;
    std::string mirror_path;
    
    // 检测到的角点
    std::vector<cv::Point2f> normal_corners;
    std::vector<cv::Point2f> mirror_corners;
    bool corners_found;
    
    ImagePair() : corners_found(false) {}
};

class CameraCalibrator {
public:
    CameraCalibrator();
    ~CameraCalibrator();
    
    // 设置棋盘格参数
    void setBoardSize(int width, int height);
    void setSquareSize(float size);
    
    // 设置相机内参（如果已知）
    void setCameraMatrix(const cv::Mat& camera_matrix, const cv::Mat& dist_coeffs);
    
    // 加载图像对
    bool loadImagePairs(const std::string& normal_dir, const std::string& mirror_dir);
    
    // 检测棋盘格角点
    bool detectChessboardCorners();
    
    // 执行标定
    bool calibrate();
    
    // 获取标定结果
    const CalibrationData& getCalibrationData() const { return calib_data_; }
    
    // 验证标定精度
    double validateCalibration();
    
    // 保存标定结果
    bool saveCalibrationResults(const std::string& filename);
    
    // 加载标定结果
    bool loadCalibrationResults(const std::string& filename);
    
    // 应用变换到点云（示例接口）
    void transformPointCloud(const std::vector<cv::Point3f>& input_points,
                            std::vector<cv::Point3f>& output_points);
    
    // 可视化功能
    void visualizeDetectedCorners(bool save_images = false);
    void visualizeCalibrationResults();

private:
    CalibrationData calib_data_;
    std::vector<ImagePair> image_pairs_;
    
    // 3D棋盘格角点（世界坐标系）
    std::vector<std::vector<cv::Point3f>> object_points_;
    
    // 内部辅助函数
    void generateObjectPoints();
    bool findChessboardInImage(const cv::Mat& image, std::vector<cv::Point2f>& corners);
    void processMirrorImage(cv::Mat& mirror_image, std::vector<cv::Point2f>& mirror_corners);
    bool solvePnPForPose(const std::vector<cv::Point2f>& image_points,
                        const std::vector<cv::Point3f>& object_points,
                        cv::Mat& rvec, cv::Mat& tvec);
    double calculateReprojectionError(const std::vector<cv::Point2f>& image_points,
                                    const std::vector<cv::Point3f>& object_points,
                                    const cv::Mat& rvec, const cv::Mat& tvec);
    bool computeTransformation(const std::vector<cv::Mat>& normal_rvecs,
                              const std::vector<cv::Mat>& normal_tvecs,
                              const std::vector<cv::Mat>& mirror_rvecs,
                              const std::vector<cv::Mat>& mirror_tvecs);

    // 镜像几何处理
    void correctMirrorGeometry(std::vector<cv::Point2f>& mirror_corners);
    cv::Mat estimateMirrorPlane(const std::vector<cv::Point3f>& normal_points,
                               const std::vector<cv::Point3f>& mirror_points);
};

#endif // CAMERA_CALIBRATOR_H
