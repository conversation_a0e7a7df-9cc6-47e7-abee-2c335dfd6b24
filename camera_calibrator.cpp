#include "camera_calibrator.h"
#include <opencv2/calib3d.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include <iostream>
#include <filesystem>
#include <algorithm>

CameraCalibrator::CameraCalibrator() {
    // 默认棋盘格参数
    calib_data_.board_size = cv::Size(11, 8);  // 内角点数量
    calib_data_.square_size = 10.0f;  // 方格大小，单位mm
}

CameraCalibrator::~CameraCalibrator() {}

void CameraCalibrator::setBoardSize(int width, int height) {
    calib_data_.board_size = cv::Size(width, height);
    generateObjectPoints();
}

void CameraCalibrator::setSquareSize(float size) {
    calib_data_.square_size = size;
    generateObjectPoints();
}

void CameraCalibrator::setCameraMatrix(const cv::Mat& camera_matrix, const cv::Mat& dist_coeffs) {
    calib_data_.camera_matrix = camera_matrix.clone();
    calib_data_.dist_coeffs = dist_coeffs.clone();
}

bool CameraCalibrator::loadImagePairs(const std::string& normal_dir, const std::string& mirror_dir) {
    image_pairs_.clear();
    
    std::vector<std::string> normal_files, mirror_files;
    
    // 获取文件列表
    for (const auto& entry : std::filesystem::directory_iterator(normal_dir)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".jpg" || ext == ".png" || ext == ".bmp") {
                normal_files.push_back(entry.path().string());
            }
        }
    }
    
    for (const auto& entry : std::filesystem::directory_iterator(mirror_dir)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".jpg" || ext == ".png" || ext == ".bmp") {
                mirror_files.push_back(entry.path().string());
            }
        }
    }
    
    // 排序文件名
    std::sort(normal_files.begin(), normal_files.end());
    std::sort(mirror_files.begin(), mirror_files.end());
    
    // 配对图像
    size_t min_count = std::min(normal_files.size(), mirror_files.size());
    for (size_t i = 0; i < min_count; ++i) {
        ImagePair pair;
        pair.normal_path = normal_files[i];
        pair.mirror_path = mirror_files[i];
        
        pair.normal_image = cv::imread(pair.normal_path);
        pair.mirror_image = cv::imread(pair.mirror_path);
        
        if (pair.normal_image.empty() || pair.mirror_image.empty()) {
            std::cerr << "Failed to load image pair: " << pair.normal_path 
                      << " or " << pair.mirror_path << std::endl;
            continue;
        }
        
        image_pairs_.push_back(pair);
    }
    
    std::cout << "Loaded " << image_pairs_.size() << " image pairs" << std::endl;
    return !image_pairs_.empty();
}

void CameraCalibrator::generateObjectPoints() {
    object_points_.clear();
    
    std::vector<cv::Point3f> corners;
    for (int i = 0; i < calib_data_.board_size.height; ++i) {
        for (int j = 0; j < calib_data_.board_size.width; ++j) {
            corners.push_back(cv::Point3f(j * calib_data_.square_size, 
                                        i * calib_data_.square_size, 0));
        }
    }
    
    // 为每个图像对生成相同的3D点
    for (size_t i = 0; i < image_pairs_.size(); ++i) {
        object_points_.push_back(corners);
    }
}

bool CameraCalibrator::findChessboardInImage(const cv::Mat& image, std::vector<cv::Point2f>& corners) {
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);

    // 尝试多种检测参数组合
    std::vector<int> flags_combinations = {
        cv::CALIB_CB_ADAPTIVE_THRESH | cv::CALIB_CB_NORMALIZE_IMAGE | cv::CALIB_CB_FAST_CHECK,
        cv::CALIB_CB_ADAPTIVE_THRESH | cv::CALIB_CB_NORMALIZE_IMAGE,
        cv::CALIB_CB_ADAPTIVE_THRESH | cv::CALIB_CB_FILTER_QUADS,
        cv::CALIB_CB_NORMALIZE_IMAGE | cv::CALIB_CB_FILTER_QUADS,
        cv::CALIB_CB_ADAPTIVE_THRESH,
        0  // 默认参数
    };

    bool found = false;
    for (int flags : flags_combinations) {
        found = cv::findChessboardCorners(gray, calib_data_.board_size, corners, flags);
        if (found) {
            std::cout << "  角点检测成功，使用参数组合: " << flags << std::endl;
            break;
        }
    }

    if (found) {
        // 亚像素精度优化
        cv::cornerSubPix(gray, corners, cv::Size(11, 11), cv::Size(-1, -1),
                        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 30, 0.1));
    }

    return found;
}

void CameraCalibrator::processMirrorImage(cv::Mat& mirror_image, std::vector<cv::Point2f>& mirror_corners) {
    // 镜像图像需要水平翻转来校正镜面反射
    cv::flip(mirror_image, mirror_image, 1);
    
    // 如果已经检测到角点，也需要翻转角点坐标
    if (!mirror_corners.empty()) {
        for (auto& corner : mirror_corners) {
            corner.x = mirror_image.cols - corner.x;
        }
    }
}

bool CameraCalibrator::detectChessboardCorners() {
    generateObjectPoints();

    int successful_detections = 0;

    for (size_t i = 0; i < image_pairs_.size(); ++i) {
        auto& pair = image_pairs_[i];
        std::cout << "处理图像对 " << (i+1) << "/" << image_pairs_.size() << "..." << std::endl;

        // 检测正常视角图像中的角点
        std::cout << "  检测正常视角图像: " << pair.normal_path << std::endl;
        bool normal_found = findChessboardInImage(pair.normal_image, pair.normal_corners);

        // 检测镜像视角图像中的角点
        std::cout << "  检测镜像视角图像: " << pair.mirror_path << std::endl;
        bool mirror_found = findChessboardInImage(pair.mirror_image, pair.mirror_corners);

        if (normal_found && mirror_found) {
            // 处理镜像图像的几何关系
            processMirrorImage(pair.mirror_image, pair.mirror_corners);
            pair.corners_found = true;
            successful_detections++;
            std::cout << "  ✓ 图像对 " << (i+1) << " 角点检测成功" << std::endl;
        } else {
            pair.corners_found = false;
            std::cout << "  ✗ 图像对 " << (i+1) << " 角点检测失败 - ";
            if (!normal_found) std::cout << "正常视角失败 ";
            if (!mirror_found) std::cout << "镜像视角失败 ";
            std::cout << std::endl;
        }
    }

    std::cout << "角点检测完成: " << successful_detections
              << "/" << image_pairs_.size() << " 对图像成功" << std::endl;

    return successful_detections > 0;
}

bool CameraCalibrator::solvePnPForPose(const std::vector<cv::Point2f>& image_points,
                                      const std::vector<cv::Point3f>& object_points,
                                      cv::Mat& rvec, cv::Mat& tvec) {
    if (calib_data_.camera_matrix.empty()) {
        std::cerr << "Camera matrix not set. Please call setCameraMatrix() first." << std::endl;
        return false;
    }
    
    return cv::solvePnP(object_points, image_points, 
                       calib_data_.camera_matrix, calib_data_.dist_coeffs,
                       rvec, tvec, false, cv::SOLVEPNP_ITERATIVE);
}

double CameraCalibrator::calculateReprojectionError(const std::vector<cv::Point2f>& image_points,
                                                   const std::vector<cv::Point3f>& object_points,
                                                   const cv::Mat& rvec, const cv::Mat& tvec) {
    std::vector<cv::Point2f> projected_points;
    cv::projectPoints(object_points, rvec, tvec,
                     calib_data_.camera_matrix, calib_data_.dist_coeffs,
                     projected_points);

    double error = 0.0;
    for (size_t i = 0; i < image_points.size(); ++i) {
        cv::Point2f diff = image_points[i] - projected_points[i];
        error += sqrt(diff.x * diff.x + diff.y * diff.y);
    }

    return error / image_points.size();
}

bool CameraCalibrator::calibrate() {
    if (image_pairs_.empty()) {
        std::cerr << "No image pairs loaded. Call loadImagePairs() first." << std::endl;
        return false;
    }

    if (calib_data_.camera_matrix.empty()) {
        std::cerr << "Camera matrix not set. Call setCameraMatrix() first." << std::endl;
        return false;
    }

    // 收集有效的图像点对
    std::vector<std::vector<cv::Point2f>> normal_image_points, mirror_image_points;
    std::vector<std::vector<cv::Point3f>> valid_object_points;

    for (size_t i = 0; i < image_pairs_.size(); ++i) {
        if (image_pairs_[i].corners_found) {
            normal_image_points.push_back(image_pairs_[i].normal_corners);
            mirror_image_points.push_back(image_pairs_[i].mirror_corners);
            valid_object_points.push_back(object_points_[i]);
        }
    }

    if (normal_image_points.size() < 3) {
        std::cerr << "Need at least 3 valid image pairs for calibration." << std::endl;
        return false;
    }

    // 计算每个视角的相机位姿
    std::vector<cv::Mat> normal_rvecs, normal_tvecs;
    std::vector<cv::Mat> mirror_rvecs, mirror_tvecs;

    // 计算正常视角的位姿
    for (size_t i = 0; i < normal_image_points.size(); ++i) {
        cv::Mat rvec, tvec;
        if (solvePnPForPose(normal_image_points[i], valid_object_points[i], rvec, tvec)) {
            normal_rvecs.push_back(rvec);
            normal_tvecs.push_back(tvec);
        }
    }

    // 计算镜像视角的位姿
    for (size_t i = 0; i < mirror_image_points.size(); ++i) {
        cv::Mat rvec, tvec;
        if (solvePnPForPose(mirror_image_points[i], valid_object_points[i], rvec, tvec)) {
            mirror_rvecs.push_back(rvec);
            mirror_tvecs.push_back(tvec);
        }
    }

    if (normal_rvecs.size() != mirror_rvecs.size() || normal_rvecs.empty()) {
        std::cerr << "Failed to compute poses for image pairs." << std::endl;
        return false;
    }

    // 计算从镜像视角到正常视角的变换
    return computeTransformation(normal_rvecs, normal_tvecs, mirror_rvecs, mirror_tvecs);
}

bool CameraCalibrator::computeTransformation(const std::vector<cv::Mat>& normal_rvecs,
                                           const std::vector<cv::Mat>& normal_tvecs,
                                           const std::vector<cv::Mat>& mirror_rvecs,
                                           const std::vector<cv::Mat>& mirror_tvecs) {
    // 使用多个位姿对计算平均变换
    std::vector<cv::Mat> relative_rvecs, relative_tvecs;

    for (size_t i = 0; i < normal_rvecs.size(); ++i) {
        // 将旋转向量转换为旋转矩阵
        cv::Mat R_normal, R_mirror;
        cv::Rodrigues(normal_rvecs[i], R_normal);
        cv::Rodrigues(mirror_rvecs[i], R_mirror);

        // 计算相对变换: T_normal = T_rel * T_mirror
        // 因此: T_rel = T_normal * T_mirror^(-1)
        cv::Mat R_rel = R_normal * R_mirror.t();
        cv::Mat t_rel = normal_tvecs[i] - R_rel * mirror_tvecs[i];

        cv::Mat rvec_rel;
        cv::Rodrigues(R_rel, rvec_rel);

        relative_rvecs.push_back(rvec_rel);
        relative_tvecs.push_back(t_rel);
    }

    // 计算平均变换
    cv::Mat avg_rvec = cv::Mat::zeros(3, 1, CV_64F);
    cv::Mat avg_tvec = cv::Mat::zeros(3, 1, CV_64F);

    for (size_t i = 0; i < relative_rvecs.size(); ++i) {
        avg_rvec += relative_rvecs[i];
        avg_tvec += relative_tvecs[i];
    }

    avg_rvec /= static_cast<double>(relative_rvecs.size());
    avg_tvec /= static_cast<double>(relative_tvecs.size());

    // 保存结果
    cv::Rodrigues(avg_rvec, calib_data_.R_mirror_to_normal);
    calib_data_.T_mirror_to_normal = avg_tvec.clone();

    // 计算重投影误差
    calib_data_.reprojection_error = validateCalibration();

    std::cout << "Calibration completed successfully!" << std::endl;
    std::cout << "Reprojection error: " << calib_data_.reprojection_error << " pixels" << std::endl;

    return true;
}

double CameraCalibrator::validateCalibration() {
    if (calib_data_.R_mirror_to_normal.empty() || calib_data_.T_mirror_to_normal.empty()) {
        return -1.0;
    }

    double total_error = 0.0;
    int valid_pairs = 0;

    for (const auto& pair : image_pairs_) {
        if (!pair.corners_found) continue;

        // 计算正常视角的重投影误差
        cv::Mat rvec_normal, tvec_normal;
        if (solvePnPForPose(pair.normal_corners, object_points_[0], rvec_normal, tvec_normal)) {
            double error_normal = calculateReprojectionError(pair.normal_corners, object_points_[0],
                                                            rvec_normal, tvec_normal);

            // 计算镜像视角变换后的重投影误差
            cv::Mat rvec_mirror, tvec_mirror;
            if (solvePnPForPose(pair.mirror_corners, object_points_[0], rvec_mirror, tvec_mirror)) {
                // 应用变换
                cv::Mat R_mirror;
                cv::Rodrigues(rvec_mirror, R_mirror);
                cv::Mat R_transformed = calib_data_.R_mirror_to_normal * R_mirror;
                cv::Mat t_transformed = calib_data_.R_mirror_to_normal * tvec_mirror + calib_data_.T_mirror_to_normal;

                cv::Mat rvec_transformed;
                cv::Rodrigues(R_transformed, rvec_transformed);

                double error_mirror = calculateReprojectionError(pair.normal_corners, object_points_[0],
                                                                rvec_transformed, t_transformed);

                total_error += (error_normal + error_mirror) / 2.0;
                valid_pairs++;
            }
        }
    }

    return valid_pairs > 0 ? total_error / valid_pairs : -1.0;
}

void CameraCalibrator::transformPointCloud(const std::vector<cv::Point3f>& input_points,
                                          std::vector<cv::Point3f>& output_points) {
    if (calib_data_.R_mirror_to_normal.empty() || calib_data_.T_mirror_to_normal.empty()) {
        std::cerr << "Calibration not completed. Cannot transform point cloud." << std::endl;
        return;
    }

    output_points.clear();
    output_points.reserve(input_points.size());

    for (const auto& point : input_points) {
        cv::Mat point_mat = (cv::Mat_<double>(3, 1) << point.x, point.y, point.z);
        cv::Mat transformed = calib_data_.R_mirror_to_normal * point_mat + calib_data_.T_mirror_to_normal;

        output_points.emplace_back(transformed.at<double>(0, 0),
                                  transformed.at<double>(1, 0),
                                  transformed.at<double>(2, 0));
    }
}

bool CameraCalibrator::saveCalibrationResults(const std::string& filename) {
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return false;
    }

    fs << "camera_matrix" << calib_data_.camera_matrix;
    fs << "dist_coeffs" << calib_data_.dist_coeffs;
    fs << "R_mirror_to_normal" << calib_data_.R_mirror_to_normal;
    fs << "T_mirror_to_normal" << calib_data_.T_mirror_to_normal;
    fs << "reprojection_error" << calib_data_.reprojection_error;
    fs << "board_width" << calib_data_.board_size.width;
    fs << "board_height" << calib_data_.board_size.height;
    fs << "square_size" << calib_data_.square_size;

    fs.release();
    std::cout << "Calibration results saved to: " << filename << std::endl;
    return true;
}

bool CameraCalibrator::loadCalibrationResults(const std::string& filename) {
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        std::cerr << "Failed to open file for reading: " << filename << std::endl;
        return false;
    }

    fs["camera_matrix"] >> calib_data_.camera_matrix;
    fs["dist_coeffs"] >> calib_data_.dist_coeffs;
    fs["R_mirror_to_normal"] >> calib_data_.R_mirror_to_normal;
    fs["T_mirror_to_normal"] >> calib_data_.T_mirror_to_normal;
    fs["reprojection_error"] >> calib_data_.reprojection_error;

    int width, height;
    fs["board_width"] >> width;
    fs["board_height"] >> height;
    calib_data_.board_size = cv::Size(width, height);
    fs["square_size"] >> calib_data_.square_size;

    fs.release();
    std::cout << "Calibration results loaded from: " << filename << std::endl;
    return true;
}

void CameraCalibrator::visualizeDetectedCorners(bool save_images) {
    for (size_t i = 0; i < image_pairs_.size(); ++i) {
        const auto& pair = image_pairs_[i];
        if (!pair.corners_found) continue;

        // 可视化正常视角
        cv::Mat normal_vis = pair.normal_image.clone();
        cv::drawChessboardCorners(normal_vis, calib_data_.board_size,
                                 pair.normal_corners, true);

        // 可视化镜像视角
        cv::Mat mirror_vis = pair.mirror_image.clone();
        cv::drawChessboardCorners(mirror_vis, calib_data_.board_size,
                                 pair.mirror_corners, true);

        // 创建并排显示
        cv::Mat combined;
        cv::hconcat(normal_vis, mirror_vis, combined);

        // 添加标题
        cv::putText(combined, "Normal View", cv::Point(50, 50),
                   cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);
        cv::putText(combined, "Mirror View", cv::Point(normal_vis.cols + 50, 50),
                   cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);

        std::string window_name = "Detected Corners - Pair " + std::to_string(i);
        cv::imshow(window_name, combined);

        if (save_images) {
            std::string save_path = "corners_pair_" + std::to_string(i) + ".jpg";
            cv::imwrite(save_path, combined);
        }

        cv::waitKey(0);
        cv::destroyWindow(window_name);
    }
}

void CameraCalibrator::visualizeCalibrationResults() {
    if (calib_data_.R_mirror_to_normal.empty() || calib_data_.T_mirror_to_normal.empty()) {
        std::cerr << "No calibration results to visualize." << std::endl;
        return;
    }

    std::cout << "\n=== Calibration Results ===" << std::endl;
    std::cout << "Rotation Matrix (Mirror to Normal):" << std::endl;
    std::cout << calib_data_.R_mirror_to_normal << std::endl;

    std::cout << "\nTranslation Vector (Mirror to Normal):" << std::endl;
    std::cout << calib_data_.T_mirror_to_normal << std::endl;

    std::cout << "\nReprojection Error: " << calib_data_.reprojection_error << " pixels" << std::endl;

    // 计算旋转角度
    cv::Mat rvec;
    cv::Rodrigues(calib_data_.R_mirror_to_normal, rvec);
    double angle = cv::norm(rvec) * 180.0 / CV_PI;
    std::cout << "Rotation Angle: " << angle << " degrees" << std::endl;

    // 计算平移距离
    double translation_distance = cv::norm(calib_data_.T_mirror_to_normal);
    std::cout << "Translation Distance: " << translation_distance << " mm" << std::endl;

    std::cout << "=========================" << std::endl;
}
