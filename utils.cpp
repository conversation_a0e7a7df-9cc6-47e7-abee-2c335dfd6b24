#include "utils.h"
#include <filesystem>
#include <algorithm>
#include <iostream>
#include <fstream>

namespace utils {

std::vector<std::string> getImageFiles(const std::string& directory) {
    std::vector<std::string> image_files;
    
    if (!std::filesystem::exists(directory)) {
        std::cerr << "Directory does not exist: " << directory << std::endl;
        return image_files;
    }
    
    for (const auto& entry : std::filesystem::directory_iterator(directory)) {
        if (entry.is_regular_file()) {
            std::string ext = entry.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            
            if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || 
                ext == ".bmp" || ext == ".tiff" || ext == ".tif") {
                image_files.push_back(entry.path().string());
            }
        }
    }
    
    std::sort(image_files.begin(), image_files.end());
    return image_files;
}

bool createDirectory(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (const std::exception& e) {
        std::cerr << "Failed to create directory " << path << ": " << e.what() << std::endl;
        return false;
    }
}

cv::Mat enhanceImage(const cv::Mat& image) {
    cv::Mat enhanced;
    
    // 转换为灰度图进行处理
    cv::Mat gray;
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = image.clone();
    }
    
    // 直方图均衡化
    cv::Mat equalized;
    cv::equalizeHist(gray, equalized);
    
    // 如果原图是彩色的，转换回彩色
    if (image.channels() == 3) {
        cv::cvtColor(equalized, enhanced, cv::COLOR_GRAY2BGR);
    } else {
        enhanced = equalized;
    }
    
    return enhanced;
}

void resizeImageKeepAspect(const cv::Mat& input, cv::Mat& output, cv::Size target_size) {
    double scale_x = static_cast<double>(target_size.width) / input.cols;
    double scale_y = static_cast<double>(target_size.height) / input.rows;
    double scale = std::min(scale_x, scale_y);
    
    cv::Size new_size(static_cast<int>(input.cols * scale), 
                      static_cast<int>(input.rows * scale));
    
    cv::resize(input, output, new_size);
}

void printCalibrationInfo(const cv::Mat& camera_matrix, const cv::Mat& dist_coeffs) {
    std::cout << "\n=== Camera Calibration Parameters ===" << std::endl;
    std::cout << "Camera Matrix:" << std::endl;
    std::cout << camera_matrix << std::endl;
    
    std::cout << "\nDistortion Coefficients:" << std::endl;
    std::cout << dist_coeffs << std::endl;
    
    // 提取焦距和主点
    double fx = camera_matrix.at<double>(0, 0);
    double fy = camera_matrix.at<double>(1, 1);
    double cx = camera_matrix.at<double>(0, 2);
    double cy = camera_matrix.at<double>(1, 2);
    
    std::cout << "\nFocal Length: fx=" << fx << ", fy=" << fy << std::endl;
    std::cout << "Principal Point: cx=" << cx << ", cy=" << cy << std::endl;
    std::cout << "====================================" << std::endl;
}

void printTransformationInfo(const cv::Mat& R, const cv::Mat& T) {
    std::cout << "\n=== Transformation Parameters ===" << std::endl;
    std::cout << "Rotation Matrix:" << std::endl;
    std::cout << R << std::endl;
    
    std::cout << "\nTranslation Vector:" << std::endl;
    std::cout << T << std::endl;
    
    // 计算旋转角度
    cv::Mat rvec;
    cv::Rodrigues(R, rvec);
    double angle = cv::norm(rvec) * 180.0 / CV_PI;
    
    // 计算平移距离
    double distance = cv::norm(T);
    
    std::cout << "\nRotation Angle: " << angle << " degrees" << std::endl;
    std::cout << "Translation Distance: " << distance << " units" << std::endl;
    std::cout << "=================================" << std::endl;
}

double calculateAngleBetweenVectors(const cv::Vec3d& v1, const cv::Vec3d& v2) {
    double dot_product = v1.dot(v2);
    double norm1 = cv::norm(v1);
    double norm2 = cv::norm(v2);
    
    if (norm1 == 0 || norm2 == 0) {
        return 0.0;
    }
    
    double cos_angle = dot_product / (norm1 * norm2);
    cos_angle = std::max(-1.0, std::min(1.0, cos_angle)); // 限制在[-1, 1]范围内
    
    return std::acos(cos_angle) * 180.0 / CV_PI;
}

cv::Mat createTransformationMatrix(const cv::Mat& R, const cv::Mat& T) {
    cv::Mat transform = cv::Mat::eye(4, 4, CV_64F);
    
    // 复制旋转矩阵
    R.copyTo(transform(cv::Rect(0, 0, 3, 3)));
    
    // 复制平移向量
    T.copyTo(transform(cv::Rect(3, 0, 1, 3)));
    
    return transform;
}

bool validateImagePair(const cv::Mat& img1, const cv::Mat& img2) {
    if (img1.empty() || img2.empty()) {
        return false;
    }
    
    // 检查图像尺寸是否相似（允许一定误差）
    double size_ratio = static_cast<double>(img1.total()) / img2.total();
    if (size_ratio < 0.5 || size_ratio > 2.0) {
        std::cout << "Warning: Image sizes differ significantly" << std::endl;
    }
    
    return true;
}

double calculateImageSimilarity(const cv::Mat& img1, const cv::Mat& img2) {
    if (img1.size() != img2.size()) {
        return 0.0;
    }
    
    cv::Mat gray1, gray2;
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1;
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2;
    }
    
    // 计算结构相似性的简化版本
    cv::Scalar mean1, mean2, stddev1, stddev2;
    cv::meanStdDev(gray1, mean1, stddev1);
    cv::meanStdDev(gray2, mean2, stddev2);
    
    cv::Mat diff;
    cv::absdiff(gray1, gray2, diff);
    cv::Scalar mean_diff = cv::mean(diff);
    
    // 简单的相似性度量
    double similarity = 1.0 - (mean_diff[0] / 255.0);
    return std::max(0.0, similarity);
}

bool loadConfig(const std::string& config_file, CalibrationConfig& config) {
    cv::FileStorage fs(config_file, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        std::cerr << "Failed to open config file: " << config_file << std::endl;
        return false;
    }
    
    int board_width, board_height;
    fs["board_width"] >> board_width;
    fs["board_height"] >> board_height;
    config.board_size = cv::Size(board_width, board_height);
    
    fs["square_size"] >> config.square_size;
    fs["normal_images_dir"] >> config.normal_images_dir;
    fs["mirror_images_dir"] >> config.mirror_images_dir;
    fs["output_dir"] >> config.output_dir;
    fs["save_intermediate_results"] >> config.save_intermediate_results;
    fs["show_visualization"] >> config.show_visualization;
    
    fs.release();
    return true;
}

bool saveConfig(const std::string& config_file, const CalibrationConfig& config) {
    cv::FileStorage fs(config_file, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        std::cerr << "Failed to create config file: " << config_file << std::endl;
        return false;
    }
    
    fs << "board_width" << config.board_size.width;
    fs << "board_height" << config.board_size.height;
    fs << "square_size" << config.square_size;
    fs << "normal_images_dir" << config.normal_images_dir;
    fs << "mirror_images_dir" << config.mirror_images_dir;
    fs << "output_dir" << config.output_dir;
    fs << "save_intermediate_results" << config.save_intermediate_results;
    fs << "show_visualization" << config.show_visualization;
    
    fs.release();
    return true;
}

} // namespace utils
