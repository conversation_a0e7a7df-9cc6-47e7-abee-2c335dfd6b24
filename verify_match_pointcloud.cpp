#include <iostream>
#include <fstream>
#include <chrono>
#include <opencv2/opencv.hpp>
#include <pcl/io/ply_io.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/registration/icp.h>

typedef pcl::PointXYZRGB PointT;
typedef pcl::PointCloud<PointT> PointCloudT;

class PointCloudMatcher {
private:
    cv::Mat R_mirror_to_normal;
    cv::Mat T_mirror_to_normal;
    
public:
    PointCloudMatcher() {}
    
    // 加载标定结果
    bool loadCalibrationResults(const std::string& filename) {
        cv::FileStorage fs(filename, cv::FileStorage::READ);
        if (!fs.isOpened()) {
            std::cerr << "无法打开标定结果文件: " << filename << std::endl;
            return false;
        }
        
        fs["R_mirror_to_normal"] >> R_mirror_to_normal;
        fs["T_mirror_to_normal"] >> T_mirror_to_normal;
        
        if (R_mirror_to_normal.empty() || T_mirror_to_normal.empty()) {
            std::cerr << "标定结果文件中缺少R或T矩阵" << std::endl;
            return false;
        }
        
        std::cout << "成功加载标定结果:" << std::endl;
        std::cout << "R矩阵: " << R_mirror_to_normal << std::endl;
        std::cout << "T向量: " << T_mirror_to_normal << std::endl;
        
        fs.release();
        return true;
    }
    
    // 加载点云文件
    PointCloudT::Ptr loadPointCloud(const std::string& filename) {
        PointCloudT::Ptr cloud(new PointCloudT);
        
        if (pcl::io::loadPLYFile<PointT>(filename, *cloud) == -1) {
            std::cerr << "无法加载点云文件: " << filename << std::endl;
            return nullptr;
        }
        
        std::cout << "成功加载点云: " << filename << std::endl;
        std::cout << "点云包含 " << cloud->size() << " 个点" << std::endl;
        
        return cloud;
    }
    
    // 将OpenCV的R,T矩阵转换为PCL的变换矩阵
    Eigen::Matrix4f createTransformMatrix() {
        Eigen::Matrix4f transform = Eigen::Matrix4f::Identity();
        
        // 复制旋转矩阵
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                transform(i, j) = R_mirror_to_normal.at<double>(i, j);
            }
        }
        
        // 复制平移向量 (注意单位转换，如果需要的话)
        transform(0, 3) = T_mirror_to_normal.at<double>(0) / 1000.0; // mm转m
        transform(1, 3) = T_mirror_to_normal.at<double>(1) / 1000.0;
        transform(2, 3) = T_mirror_to_normal.at<double>(2) / 1000.0;
        
        std::cout << "变换矩阵:" << std::endl;
        std::cout << transform << std::endl;
        
        return transform;
    }
    
    // 变换点云
    PointCloudT::Ptr transformPointCloud(PointCloudT::Ptr input_cloud) {
        if (!input_cloud || input_cloud->empty()) {
            std::cerr << "输入点云为空" << std::endl;
            return nullptr;
        }
        
        PointCloudT::Ptr transformed_cloud(new PointCloudT);
        Eigen::Matrix4f transform = createTransformMatrix();
        
        pcl::transformPointCloud(*input_cloud, *transformed_cloud, transform);
        
        std::cout << "点云变换完成，变换后点云包含 " << transformed_cloud->size() << " 个点" << std::endl;
        
        return transformed_cloud;
    }
    
    // 下采样点云（可选，用于提高可视化性能）
    PointCloudT::Ptr downsamplePointCloud(PointCloudT::Ptr input_cloud, float leaf_size = 0.01f) {
        if (!input_cloud || input_cloud->empty()) {
            return input_cloud;
        }
        
        PointCloudT::Ptr filtered_cloud(new PointCloudT);
        pcl::VoxelGrid<PointT> voxel_filter;
        voxel_filter.setInputCloud(input_cloud);
        voxel_filter.setLeafSize(leaf_size, leaf_size, leaf_size);
        voxel_filter.filter(*filtered_cloud);
        
        std::cout << "下采样完成: " << input_cloud->size() << " -> " << filtered_cloud->size() << " 点" << std::endl;
        
        return filtered_cloud;
    }
    
    // 保存点云到文件
    bool savePointCloud(PointCloudT::Ptr cloud, const std::string& filename) {
        if (!cloud || cloud->empty()) {
            std::cerr << "点云为空，无法保存" << std::endl;
            return false;
        }

        if (pcl::io::savePLYFileBinary(filename, *cloud) == -1) {
            std::cerr << "保存点云失败: " << filename << std::endl;
            return false;
        }

        std::cout << "成功保存点云到: " << filename << std::endl;
        std::cout << "保存了 " << cloud->size() << " 个点" << std::endl;
        return true;
    }
    
    // 计算配准精度（使用ICP进行精细配准并计算误差）
    double calculateRegistrationError(PointCloudT::Ptr source, PointCloudT::Ptr target) {
        if (!source || !target || source->empty() || target->empty()) {
            std::cerr << "点云为空，无法计算配准误差" << std::endl;
            return -1.0;
        }
        
        // 使用ICP进行精细配准
        pcl::IterativeClosestPoint<PointT, PointT> icp;
        icp.setInputSource(source);
        icp.setInputTarget(target);
        icp.setMaximumIterations(50);
        icp.setTransformationEpsilon(1e-8);
        icp.setEuclideanFitnessEpsilon(1e-8);
        
        PointCloudT::Ptr aligned_cloud(new PointCloudT);
        icp.align(*aligned_cloud);
        
        if (icp.hasConverged()) {
            double fitness_score = icp.getFitnessScore();
            std::cout << "ICP收敛，适应度分数: " << fitness_score << std::endl;
            std::cout << "ICP变换矩阵:" << std::endl;
            std::cout << icp.getFinalTransformation() << std::endl;
            return fitness_score;
        } else {
            std::cerr << "ICP未收敛" << std::endl;
            return -1.0;
        }
    }
};

int main(int argc, char* argv[]) {
    std::cout << "\n=== 点云配准验证程序 ===" << std::endl;

    // 解析命令行参数
    std::string calibration_file = "mirror_calibration_result.yml";
    std::string normal_pcd = "";
    std::string mirror_pcd = "";
    std::string output_pcd = "";

    if (argc >= 2) calibration_file = argv[1];
    if (argc >= 3) normal_pcd = argv[2];
    if (argc >= 4) mirror_pcd = argv[3];
    if (argc >= 5) output_pcd = argv[4];

    if (normal_pcd.empty() || mirror_pcd.empty()) {
        std::cout << "用法: " << argv[0] << " [标定文件] <正常视角点云> <镜像视角点云> [输出点云]" << std::endl;
        std::cout << "示例: " << argv[0] << " mirror_calibration_result.yml normal.pcd mirror.pcd transformed_mirror.ply" << std::endl;
        return -1;
    }

    // 设置默认输出文件名
    if (output_pcd.empty()) {
        output_pcd = "transformed_mirror.ply";
    }
    
    PointCloudMatcher matcher;
    
    // 步骤1: 加载标定结果
    std::cout << "\n步骤1: 加载标定结果..." << std::endl;
    if (!matcher.loadCalibrationResults(calibration_file)) {
        return -1;
    }
    
    // 步骤2: 加载点云数据
    std::cout << "\n步骤2: 加载点云数据..." << std::endl;
    auto normal_cloud = matcher.loadPointCloud(normal_pcd);
    auto mirror_cloud = matcher.loadPointCloud(mirror_pcd);
    
    if (!normal_cloud || !mirror_cloud) {
        std::cerr << "点云加载失败" << std::endl;
        return -1;
    }
    
    // 步骤3: 变换镜像视角点云
    std::cout << "\n步骤3: 变换镜像视角点云..." << std::endl;
    auto transformed_cloud = matcher.transformPointCloud(mirror_cloud);
    
    if (!transformed_cloud) {
        std::cerr << "点云变换失败" << std::endl;
        return -1;
    }
    
    // 步骤4: 保存变换后的点云
    std::cout << "\n步骤4: 保存变换后的点云..." << std::endl;
    if (!matcher.savePointCloud(transformed_cloud, output_pcd)) {
        std::cerr << "保存变换后的点云失败" << std::endl;
        return -1;
    }

    // 步骤5: 计算配准精度（可选）
    std::cout << "\n步骤5: 计算配准精度..." << std::endl;
    auto normal_downsampled = matcher.downsamplePointCloud(normal_cloud, 0.01f);
    auto transformed_downsampled = matcher.downsamplePointCloud(transformed_cloud, 0.01f);

    double error = matcher.calculateRegistrationError(transformed_downsampled, normal_downsampled);
    if (error >= 0) {
        std::cout << "配准误差: " << error << " 米" << std::endl;
    }

    // 步骤6: 生成配准报告
    std::cout << "\n步骤6: 生成配准报告..." << std::endl;
    std::string report_file = output_pcd.substr(0, output_pcd.find_last_of('.')) + "_report.txt";
    std::ofstream report(report_file);
    if (report.is_open()) {
        report << "=== 点云配准报告 ===" << std::endl;
        report << "标定文件: " << calibration_file << std::endl;
        report << "正常视角点云: " << normal_pcd << " (" << normal_cloud->size() << " 点)" << std::endl;
        report << "镜像视角点云: " << mirror_pcd << " (" << mirror_cloud->size() << " 点)" << std::endl;
        report << "变换后点云: " << output_pcd << " (" << transformed_cloud->size() << " 点)" << std::endl;
        if (error >= 0) {
            report << "配准误差: " << error << " 米" << std::endl;
        }
        report.close();
        std::cout << "配准报告已保存到: " << report_file << std::endl;
    }

    std::cout << "\n=== 处理完成 ===" << std::endl;
    std::cout << "变换后的点云已保存到: " << output_pcd << std::endl;
    std::cout << "现在可以使用点云查看器查看配准结果" << std::endl;

    return 0;
}
