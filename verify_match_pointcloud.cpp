#include <iostream>
#include <opencv2/opencv.hpp>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/visualization/pcl_visualizer.h>
#include <pcl/common/transforms.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/registration/icp.h>

typedef pcl::PointXYZRGB PointT;
typedef pcl::PointCloud<PointT> PointCloudT;

class PointCloudMatcher {
private:
    cv::Mat R_mirror_to_normal;
    cv::Mat T_mirror_to_normal;
    
public:
    PointCloudMatcher() {}
    
    // 加载标定结果
    bool loadCalibrationResults(const std::string& filename) {
        cv::FileStorage fs(filename, cv::FileStorage::READ);
        if (!fs.isOpened()) {
            std::cerr << "无法打开标定结果文件: " << filename << std::endl;
            return false;
        }
        
        fs["R_mirror_to_normal"] >> R_mirror_to_normal;
        fs["T_mirror_to_normal"] >> T_mirror_to_normal;
        
        if (R_mirror_to_normal.empty() || T_mirror_to_normal.empty()) {
            std::cerr << "标定结果文件中缺少R或T矩阵" << std::endl;
            return false;
        }
        
        std::cout << "成功加载标定结果:" << std::endl;
        std::cout << "R矩阵: " << R_mirror_to_normal << std::endl;
        std::cout << "T向量: " << T_mirror_to_normal << std::endl;
        
        fs.release();
        return true;
    }
    
    // 加载点云文件
    PointCloudT::Ptr loadPointCloud(const std::string& filename) {
        PointCloudT::Ptr cloud(new PointCloudT);
        
        if (pcl::io::loadPCDFile<PointT>(filename, *cloud) == -1) {
            std::cerr << "无法加载点云文件: " << filename << std::endl;
            return nullptr;
        }
        
        std::cout << "成功加载点云: " << filename << std::endl;
        std::cout << "点云包含 " << cloud->size() << " 个点" << std::endl;
        
        return cloud;
    }
    
    // 将OpenCV的R,T矩阵转换为PCL的变换矩阵
    Eigen::Matrix4f createTransformMatrix() {
        Eigen::Matrix4f transform = Eigen::Matrix4f::Identity();
        
        // 复制旋转矩阵
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                transform(i, j) = R_mirror_to_normal.at<double>(i, j);
            }
        }
        
        // 复制平移向量 (注意单位转换，如果需要的话)
        transform(0, 3) = T_mirror_to_normal.at<double>(0) / 1000.0; // mm转m
        transform(1, 3) = T_mirror_to_normal.at<double>(1) / 1000.0;
        transform(2, 3) = T_mirror_to_normal.at<double>(2) / 1000.0;
        
        std::cout << "变换矩阵:" << std::endl;
        std::cout << transform << std::endl;
        
        return transform;
    }
    
    // 变换点云
    PointCloudT::Ptr transformPointCloud(PointCloudT::Ptr input_cloud) {
        if (!input_cloud || input_cloud->empty()) {
            std::cerr << "输入点云为空" << std::endl;
            return nullptr;
        }
        
        PointCloudT::Ptr transformed_cloud(new PointCloudT);
        Eigen::Matrix4f transform = createTransformMatrix();
        
        pcl::transformPointCloud(*input_cloud, *transformed_cloud, transform);
        
        std::cout << "点云变换完成，变换后点云包含 " << transformed_cloud->size() << " 个点" << std::endl;
        
        return transformed_cloud;
    }
    
    // 下采样点云（可选，用于提高可视化性能）
    PointCloudT::Ptr downsamplePointCloud(PointCloudT::Ptr input_cloud, float leaf_size = 0.01f) {
        if (!input_cloud || input_cloud->empty()) {
            return input_cloud;
        }
        
        PointCloudT::Ptr filtered_cloud(new PointCloudT);
        pcl::VoxelGrid<PointT> voxel_filter;
        voxel_filter.setInputCloud(input_cloud);
        voxel_filter.setLeafSize(leaf_size, leaf_size, leaf_size);
        voxel_filter.filter(*filtered_cloud);
        
        std::cout << "下采样完成: " << input_cloud->size() << " -> " << filtered_cloud->size() << " 点" << std::endl;
        
        return filtered_cloud;
    }
    
    // 可视化点云配准结果
    void visualizePointClouds(PointCloudT::Ptr normal_cloud, 
                             PointCloudT::Ptr mirror_cloud, 
                             PointCloudT::Ptr transformed_cloud) {
        pcl::visualization::PCLVisualizer::Ptr viewer(new pcl::visualization::PCLVisualizer("点云配准结果"));
        
        // 设置背景颜色
        viewer->setBackgroundColor(0, 0, 0);
        
        // 添加正常视角点云（绿色）
        if (normal_cloud && !normal_cloud->empty()) {
            pcl::visualization::PointCloudColorHandlerCustom<PointT> normal_color(normal_cloud, 0, 255, 0);
            viewer->addPointCloud<PointT>(normal_cloud, normal_color, "normal_cloud");
            viewer->setPointCloudRenderingProperties(pcl::visualization::PCL_VISUALIZER_POINT_SIZE, 2, "normal_cloud");
        }
        
        // 添加原始镜像视角点云（红色）
        if (mirror_cloud && !mirror_cloud->empty()) {
            pcl::visualization::PointCloudColorHandlerCustom<PointT> mirror_color(mirror_cloud, 255, 0, 0);
            viewer->addPointCloud<PointT>(mirror_cloud, mirror_color, "mirror_cloud");
            viewer->setPointCloudRenderingProperties(pcl::visualization::PCL_VISUALIZER_POINT_SIZE, 2, "mirror_cloud");
        }
        
        // 添加变换后的镜像视角点云（蓝色）
        if (transformed_cloud && !transformed_cloud->empty()) {
            pcl::visualization::PointCloudColorHandlerCustom<PointT> transformed_color(transformed_cloud, 0, 0, 255);
            viewer->addPointCloud<PointT>(transformed_cloud, transformed_color, "transformed_cloud");
            viewer->setPointCloudRenderingProperties(pcl::visualization::PCL_VISUALIZER_POINT_SIZE, 2, "transformed_cloud");
        }
        
        // 添加坐标系
        viewer->addCoordinateSystem(0.1);
        
        // 添加图例
        viewer->addText("绿色: 正常视角点云", 10, 70, 16, 0, 1, 0, "legend1");
        viewer->addText("红色: 原始镜像视角点云", 10, 50, 16, 1, 0, 0, "legend2");
        viewer->addText("蓝色: 变换后镜像视角点云", 10, 30, 16, 0, 0, 1, "legend3");
        viewer->addText("按 'q' 退出", 10, 10, 16, 1, 1, 1, "instruction");
        
        // 设置相机视角
        viewer->initCameraParameters();
        
        // 显示
        while (!viewer->wasStopped()) {
            viewer->spinOnce(100);
        }
    }
    
    // 计算配准精度（使用ICP进行精细配准并计算误差）
    double calculateRegistrationError(PointCloudT::Ptr source, PointCloudT::Ptr target) {
        if (!source || !target || source->empty() || target->empty()) {
            std::cerr << "点云为空，无法计算配准误差" << std::endl;
            return -1.0;
        }
        
        // 使用ICP进行精细配准
        pcl::IterativeClosestPoint<PointT, PointT> icp;
        icp.setInputSource(source);
        icp.setInputTarget(target);
        icp.setMaximumIterations(50);
        icp.setTransformationEpsilon(1e-8);
        icp.setEuclideanFitnessEpsilon(1e-8);
        
        PointCloudT::Ptr aligned_cloud(new PointCloudT);
        icp.align(*aligned_cloud);
        
        if (icp.hasConverged()) {
            double fitness_score = icp.getFitnessScore();
            std::cout << "ICP收敛，适应度分数: " << fitness_score << std::endl;
            std::cout << "ICP变换矩阵:" << std::endl;
            std::cout << icp.getFinalTransformation() << std::endl;
            return fitness_score;
        } else {
            std::cerr << "ICP未收敛" << std::endl;
            return -1.0;
        }
    }
};

int main(int argc, char* argv[]) {
    std::cout << "\n=== 点云配准验证程序 ===" << std::endl;
    
    // 解析命令行参数
    std::string calibration_file = "mirror_calibration_result.yml";
    std::string normal_pcd = "";
    std::string mirror_pcd = "";
    
    if (argc >= 2) calibration_file = argv[1];
    if (argc >= 3) normal_pcd = argv[2];
    if (argc >= 4) mirror_pcd = argv[3];
    
    if (normal_pcd.empty() || mirror_pcd.empty()) {
        std::cout << "用法: " << argv[0] << " [标定文件] <正常视角点云> <镜像视角点云>" << std::endl;
        std::cout << "示例: " << argv[0] << " mirror_calibration_result.yml normal.pcd mirror.pcd" << std::endl;
        return -1;
    }
    
    PointCloudMatcher matcher;
    
    // 步骤1: 加载标定结果
    std::cout << "\n步骤1: 加载标定结果..." << std::endl;
    if (!matcher.loadCalibrationResults(calibration_file)) {
        return -1;
    }
    
    // 步骤2: 加载点云数据
    std::cout << "\n步骤2: 加载点云数据..." << std::endl;
    auto normal_cloud = matcher.loadPointCloud(normal_pcd);
    auto mirror_cloud = matcher.loadPointCloud(mirror_pcd);
    
    if (!normal_cloud || !mirror_cloud) {
        std::cerr << "点云加载失败" << std::endl;
        return -1;
    }
    
    // 步骤3: 变换镜像视角点云
    std::cout << "\n步骤3: 变换镜像视角点云..." << std::endl;
    auto transformed_cloud = matcher.transformPointCloud(mirror_cloud);
    
    if (!transformed_cloud) {
        std::cerr << "点云变换失败" << std::endl;
        return -1;
    }
    
    // 步骤4: 下采样（可选，提高可视化性能）
    std::cout << "\n步骤4: 下采样点云..." << std::endl;
    auto normal_downsampled = matcher.downsamplePointCloud(normal_cloud, 0.005f);
    auto mirror_downsampled = matcher.downsamplePointCloud(mirror_cloud, 0.005f);
    auto transformed_downsampled = matcher.downsamplePointCloud(transformed_cloud, 0.005f);
    
    // 步骤5: 计算配准精度
    std::cout << "\n步骤5: 计算配准精度..." << std::endl;
    double error = matcher.calculateRegistrationError(transformed_downsampled, normal_downsampled);
    if (error >= 0) {
        std::cout << "配准误差: " << error << " 米" << std::endl;
    }
    
    // 步骤6: 可视化结果
    std::cout << "\n步骤6: 可视化配准结果..." << std::endl;
    std::cout << "启动PCL可视化器..." << std::endl;
    matcher.visualizePointClouds(normal_downsampled, mirror_downsampled, transformed_downsampled);
    
    std::cout << "程序结束" << std::endl;
    return 0;
}
